<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault>


    <!-- Hot Reload settings for Debug builds -->
    <EnableHotReload Condition="'$(Configuration)' == 'Debug'">true</EnableHotReload>
    <UseSharedCompilation Condition="'$(Configuration)' == 'Debug'">true</UseSharedCompilation>

    <!-- Disable problematic features in Debug for Hot Reload -->
    <PublishAot Condition="'$(Configuration)' == 'Debug'">false</PublishAot>
    <TrimMode Condition="'$(Configuration)' == 'Debug'">none</TrimMode>
  </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <PublishAot>true</PublishAot>
    <TrimMode>link</TrimMode>
    <PublishSingleFile>true</PublishSingleFile>
    <PublishReadyToRun>true</PublishReadyToRun>
    <!-- AOT-specific settings for Akka.NET -->
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Akka" Version="1.5.42" />
    <PackageReference Include="Akka.Remote" Version="1.5.42" />
    <PackageReference Include="Akka.Streams" Version="1.5.42" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageReference Include="Grpc.Net.Client" Version="2.71.0" />

    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="FreeSql" Version="3.2.833" />
    <PackageReference Include="FreeSql.Provider.PostgreSQL" Version="3.2.833" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\*.proto" GrpcServices="Server" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <RdXmlFile Include="rd.xml" />
  </ItemGroup>

</Project>
