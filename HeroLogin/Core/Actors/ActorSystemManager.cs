using System;
using System.Threading;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Configuration;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ ứng dụng
    /// </summary>
    public class ActorSystemManager
    {
        private static ActorSystemManager? _instance;
        private ActorSystem? _actorSystem;
        private IActorRef? _tcpManagerActor;
        private IActorRef? _packetHandlerActor;

        public static ActorSystemManager Instance => _instance ??= new ActorSystemManager();

        public ActorSystem ActorSystem => _actorSystem ?? throw new InvalidOperationException("ActorSystem chưa được khởi tạo");

        public IActorRef TcpManagerActor => _tcpManagerActor ?? throw new InvalidOperationException("TcpManagerActor chưa được khởi tạo");

        public IActorRef PacketHandlerActor => _packetHandlerActor ?? throw new InvalidOperationException("PacketHandlerActor chưa được khởi tạo");

        private ActorSystemManager()
        {
            // Private constructor for singleton pattern
        }

        /// <summary>
        /// Khởi tạo ActorSystem và các actor chính (async version for AOT compatibility)
        /// </summary>
        public async Task InitializeAsync()
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            try
            {
                await Task.Run(() =>
                {
                    try
                    {
                        // Tạo optimized configuration cho AOT compatibility
                        Logger.Instance.Info("Tạo ActorSystem với optimized configuration...");
                        var config = ConfigurationFactory.ParseString(@"
                            akka {
                                actor {
                                    default-dispatcher {
                                        type = Dispatcher
                                        executor = fork-join-executor
                                        fork-join-executor {
                                            parallelism-min = 2
                                            parallelism-factor = 2.0
                                            parallelism-max = 8
                                        }
                                        throughput = 100
                                    }

                                    default-mailbox {
                                        mailbox-type = ""Akka.Dispatch.UnboundedMailbox""
                                        mailbox-capacity = 1000
                                    }
                                }

                                io.tcp {
                                    nr-of-selectors = 1
                                    max-channels = 1024
                                    selector-association-retries = 10
                                    batch-accept-limit = 10
                                    direct-buffer-size = 128KiB
                                    direct-buffer-pool-limit = 1000
                                }
                            }
                        ");
                        _actorSystem = ActorSystem.Create("HeroLoginSystem", config);
                        Logger.Instance.Info("Tạo ActorSystem thành công với optimized configuration");
                    }
                    catch (Exception ex1)
                    {
                        Logger.Instance.Error($"Lỗi khi tạo ActorSystem với optimized configuration: {ex1.Message}");

                        try
                        {
                            // Phương pháp 2: Thử tạo ActorSystem đơn giản nhất
                            Logger.Instance.Info("Thử tạo ActorSystem với default settings...");
                            _actorSystem = ActorSystem.Create("HeroLoginSystem");
                            Logger.Instance.Info("Tạo ActorSystem thành công với default settings");
                        }
                        catch (Exception ex2)
                        {
                            Logger.Instance.Error($"Lỗi khi tạo ActorSystem với default settings: {ex2.Message}");

                            try
                            {
                                // Phương pháp 3: Sử dụng empty configuration để bypass configuration issues
                                Logger.Instance.Info("Thử tạo ActorSystem với empty configuration...");
                                var emptyConfig = ConfigurationFactory.Empty;
                                _actorSystem = ActorSystem.Create("HeroLoginSystem", emptyConfig);
                                Logger.Instance.Info("Tạo ActorSystem thành công với empty configuration");
                            }
                            catch (Exception ex3)
                            {
                                Logger.Instance.Error($"Lỗi khi tạo ActorSystem với empty configuration: {ex3.Message}");
                                throw new InvalidOperationException($"Không thể tạo ActorSystem. Lỗi 1: {ex1.Message}, Lỗi 2: {ex2.Message}, Lỗi 3: {ex3.Message}");
                            }
                        }
                    }

                    Logger.Instance.Info("ActorSystem đã được tạo thành công");

                    if (_actorSystem == null)
                    {
                        throw new InvalidOperationException("ActorSystem.Create returned null");
                    }

                    Logger.Instance.Info("Đang tạo PacketHandlerActor...");

                    // Tạo PacketHandlerActor trước vì TcpManagerActor sẽ cần tham chiếu đến nó
                    _packetHandlerActor = _actorSystem.ActorOf(Props.Create(() => new PacketHandlerActor()), "packetHandler");

                    if (_packetHandlerActor == null)
                    {
                        throw new InvalidOperationException("Failed to create PacketHandlerActor");
                    }

                    Logger.Instance.Info("PacketHandlerActor đã được tạo thành công");

                    Logger.Instance.Info("Đang tạo TcpManagerActor...");

                    // Tạo TcpManagerActor
                    _tcpManagerActor = _actorSystem.ActorOf(Props.Create(() => new TcpManagerActor(_packetHandlerActor)), "tcpManager");

                    if (_tcpManagerActor == null)
                    {
                        throw new InvalidOperationException("Failed to create TcpManagerActor");
                    }

                    Logger.Instance.Info("TcpManagerActor đã được tạo thành công");

                    // Gửi tham chiếu TcpManagerActor đến PacketHandlerActor
                    _packetHandlerActor.Tell(new SetTcpManagerActor(_tcpManagerActor));

                }, cts.Token);

                Logger.Instance.Info("ActorSystem đã được khởi tạo thành công");
            }
            catch (OperationCanceledException)
            {
                Logger.Instance.Error("Timeout khi khởi tạo ActorSystem (30 giây)");
                throw new TimeoutException("ActorSystem initialization timed out after 30 seconds");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo ActorSystem: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Logger.Instance.Error($"Inner exception: {ex.InnerException.Message}");
                    Logger.Instance.Error($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                throw;
            }
        }

        /// <summary>
        /// Khởi tạo ActorSystem và các actor chính (sync version for backward compatibility)
        /// </summary>
        public void Initialize()
        {
            InitializeAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Dừng ActorSystem
        /// </summary>
        public void Shutdown()
        {
            try
            {
                if (_actorSystem != null)
                {
                    _actorSystem.Terminate().Wait();
                    _actorSystem = null;
                    _tcpManagerActor = null;
                    _packetHandlerActor = null;

                    Logger.Instance.Info("ActorSystem đã được dừng thành công");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng ActorSystem: {ex.Message}");
            }
        }
    }
}
