using System;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// <PERSON><PERSON>c loại gói tin trong hệ thống
    /// </summary>
    public enum PacketType : ushort
    {
        // Các loại gói tin
        Ping = 0x0001,
        Login = 32768,

        Login2 = 32848,
        LoginResponse = 32769,
        ServerList2 = 32790,
        ServerList = 32849,
        ServerListResponse = 32791,
        SelectServer = 32780,
        SelectServerResponse = 32868,
        VerifyVersion = 32778,
        VerifyVersionResponse = 32779,
        Disconnect = 0x0005
    }

    /// <summary>
    /// Đ<PERSON>i diện cho một gói tin trong hệ thống
    /// </summary>
    public class Packet
    {
        public PacketType Type { get; }
        public byte[] Data { get; }
        public int Length { get; }

        public Packet(PacketType type, byte[] data)
        {
            Type = type;
            Data = data;
            Length = data.Length;
        }

        public static Packet Parse(byte[] buffer, int length)
        {
            // <PERSON><PERSON><PERSON> sử cấu trúc gói tin: [2 byte loại gói tin][2 byte độ dài][dữ liệu]
            if (length < 4)
            {
                throw new ArgumentException("Gói tin không hợp lệ: quá ngắn");
            }

            ushort type = BitConverter.ToUInt16(buffer, 0);
            ushort dataLength = BitConverter.ToUInt16(buffer, 2);

            if (length < 4 + dataLength)
            {
                throw new ArgumentException("Gói tin không hợp lệ: độ dài không khớp");
            }

            byte[] data = new byte[dataLength];
            Array.Copy(buffer, 4, data, 0, dataLength);

            return new Packet((PacketType)type, data);
        }

        public byte[] ToByteArray()
        {
            byte[] result = new byte[4 + Length];
            BitConverter.GetBytes((ushort)Type).CopyTo(result, 0);
            BitConverter.GetBytes((ushort)Length).CopyTo(result, 2);
            Data.CopyTo(result, 4);
            return result;
        }
    }
}
