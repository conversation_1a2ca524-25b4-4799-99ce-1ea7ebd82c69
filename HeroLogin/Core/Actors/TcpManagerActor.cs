using System;
using System.Collections.Generic;
using System.Net;
using Akka.Actor;
using Akka.IO;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// Actor quản lý kết nối TCP
    /// </summary>
    public class TcpManagerActor : ReceiveActor
    {
        private readonly IActorRef _packetHandlerActor;
        private readonly ConfigManager _configManager;
        private readonly Dictionary<IActorRef, ClientSession> _sessions = new Dictionary<IActorRef, ClientSession>();
        private int _nextSessionId = 1;
        private bool _isRunning = false;
        private ICancelable? _inactiveConnectionsChecker;

        public TcpManagerActor(IActorRef packetHandlerActor)
        {
            _packetHandlerActor = packetHandlerActor;
            _configManager = ConfigManager.Instance;

            // Định nghĩa các message handler
            Receive<StartServer>(msg => StartServer());
            Receive<StopServer>(msg => StopServer());
            Receive<Tcp.Connected>(msg => HandleConnected(msg));
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
            Receive<Tcp.Received>(msg => HandleReceived(msg));
            Receive<CheckInactiveConnections>(msg => CheckInactiveConnections());
            Receive<SendPacket>(msg => SendPacket(msg));
            Receive<BroadcastPacket>(msg => BroadcastPacket(msg));
            Receive<GetSessionCount>(msg => Sender.Tell(new SessionCountResponse(_sessions.Count)));
            Receive<GetAllSessions>(msg => Sender.Tell(new AllSessionsResponse(new List<ClientSession>(_sessions.Values))));
            Receive<Tcp.CommandFailed>(msg=>
            {
                if (msg.Cmd is Tcp.Write)
                {
                    Logger.Instance.Error($"Gửi gói tin thất bại: {msg.Cmd}");
                }
            });
        }

        protected override void PreStart()
        {
            // Tự động khởi động server khi actor được tạo
            Self.Tell(new StartServer());
        }

        protected override void PostStop()
        {
            // Hủy task kiểm tra kết nối không hoạt động
            _inactiveConnectionsChecker?.Cancel();
        }

        private void StartServer()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng đã đang chạy");
                return;
            }

            try
            {
                int port = _configManager.LoginServerSettings.LoginServerPort;

                // Lấy tham chiếu đến Tcp manager của Akka.IO
                var tcpManager = Context.System.Tcp();

                // Bind vào cổng được chỉ định
                tcpManager.Tell(new Tcp.Bind(Self, new IPEndPoint(IPAddress.Any, port)));

                _isRunning = true;
                Logger.Instance.Info($"Máy chủ mạng đã khởi động trên cổng {port}");

                // Bắt đầu task kiểm tra các kết nối không hoạt động
                ScheduleInactiveConnectionsCheck();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động máy chủ mạng: {ex.Message}");
            }
        }

        private void StopServer()
        {
            if (!_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng không chạy");
                return;
            }

            try
            {
                // Hủy task kiểm tra kết nối không hoạt động
                _inactiveConnectionsChecker?.Cancel();

                // Đóng tất cả các kết nối
                foreach (var session in _sessions)
                {
                    session.Key.Tell(Tcp.Close.Instance);
                }

                _sessions.Clear();
                _isRunning = false;

                Logger.Instance.Info("Máy chủ mạng đã dừng");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng máy chủ mạng: {ex.Message}");
            }
        }

        private void HandleConnected(Tcp.Connected connected)
        {
            Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} ");

            // Chấp nhận kết nối
            Sender.Tell(new Tcp.Register(Self));

            // Tạo session mới
            int sessionId = _nextSessionId++;
            var clientSession = new ClientSession(sessionId, connected.RemoteAddress, Sender);

            // Lưu session
            _sessions[Sender] = clientSession;

            // Tạo actor con để xử lý client này
            Context.ActorOf(Props.Create(() => new ClientActor(Sender, clientSession, _packetHandlerActor)), $"client-{sessionId}");

            Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} (SessionID: {sessionId})");
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed closed)
        {
            if (_sessions.TryGetValue(Sender, out var session))
            {
                Logger.Instance.Info($"Client ngắt kết nối: {session.RemoteEndPoint} (SessionID: {session.SessionId})");
                _sessions.Remove(Sender);
            }
        }

        private void HandleReceived(Tcp.Received received)
        {
            try
            {
                if (_sessions.TryGetValue(Sender, out var session))
                {
                    // Cập nhật thời gian hoạt động
                    session.UpdateActivity();

                    // Chuyển đổi ByteString thành byte array
                    byte[] data = received.Data.ToArray();

                    // Ghi log gói tin nhận được
                    PacketLogger.LogIncomingPacket(session.SessionId, data);

                    // Gửi dữ liệu đến PacketHandlerActor để xử lý
                    _packetHandlerActor.Tell(new ProcessPacket(Sender, session, data));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được: {ex.Message}");
            }
        }

        private void ScheduleInactiveConnectionsCheck()
        {
            // Kiểm tra mỗi 30 giây
            _inactiveConnectionsChecker = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                TimeSpan.FromSeconds(30),
                TimeSpan.FromSeconds(30),
                Self,
                new CheckInactiveConnections(),
                Self);
        }

        private void CheckInactiveConnections()
        {
            try
            {
                var now = DateTime.Now;
                var inactiveSessions = new List<IActorRef>();

                // Tìm các session không hoạt động trong 5 phút
                foreach (var session in _sessions)
                {
                    if ((now - session.Value.LastActivityTime).TotalMinutes > 5)
                    {
                        inactiveSessions.Add(session.Key);
                    }
                }

                // Đóng các kết nối không hoạt động
                foreach (var connection in inactiveSessions)
                {
                    if (_sessions.TryGetValue(connection, out var session))
                    {
                        Logger.Instance.Info($"Đóng kết nối không hoạt động: {session.RemoteEndPoint} (SessionID: {session.SessionId})");
                        connection.Tell(Tcp.Close.Instance);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra kết nối không hoạt động: {ex.Message}");
            }
        }

        private void SendPacket(SendPacket message)
        {
            try
            {
                if (_sessions.TryGetValue(message.Connection, out var session))
                {
                    // Ghi log gói tin gửi đi
                    PacketLogger.LogOutgoingPacket(session.SessionId, message.Data);
                    
                    Logger.Instance.Debug($"Gửi gói tin đến connection {session.SessionId}, độ dài: {message.Data.Length} bytes");
                    
                    // Gửi gói tin đến connection
                    message.Connection.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    
                    Logger.Instance.Debug($"Đã gửi gói tin đến connection {session.SessionId}");
                    
                    session.UpdateActivity();
                }
                else
                {
                    Logger.Instance.Warning($"Không tìm thấy session cho connection khi gửi gói tin");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi gói tin: {ex.Message}");
            }
        }

        private void BroadcastPacket(BroadcastPacket message)
        {
            try
            {
                foreach (var session in _sessions)
                {
                    if (message.Filter == null || message.Filter(session.Value))
                    {
                        // Ghi log gói tin gửi đi
                        PacketLogger.LogOutgoingPacket(session.Value.SessionId, message.Data);

                        session.Key.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi broadcast gói tin: {ex.Message}");
            }
        }
    }

    // Các message classes
    public class StartServer { }
    public class StopServer { }
    public class CheckInactiveConnections { }

    public class SendPacket
    {
        public IActorRef Connection { get; }
        public byte[] Data { get; }

        public SendPacket(IActorRef connection, byte[] data)
        {
            Connection = connection;
            Data = data;
        }
    }

    public class BroadcastPacket
    {
        public byte[] Data { get; }
        public Predicate<ClientSession>? Filter { get; }

        public BroadcastPacket(byte[] data, Predicate<ClientSession>? filter = null)
        {
            Data = data;
            Filter = filter;
        }
    }

    public class GetSessionCount { }

    public class SessionCountResponse
    {
        public int Count { get; }

        public SessionCountResponse(int count)
        {
            Count = count;
        }
    }

    public class GetAllSessions { }

    public class AllSessionsResponse
    {
        public List<ClientSession> Sessions { get; }

        public AllSessionsResponse(List<ClientSession> sessions)
        {
            Sessions = sessions;
        }
    }
}
