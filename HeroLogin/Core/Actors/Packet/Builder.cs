using System.Security.Authentication.ExtendedProtection;
using HeroLogin.Services;

namespace HeroLogin.Core.Actors
{
    public class Builder
    {
        public static byte[] BuildLoginSuccessResponse(string account, string password)
        {
            using var w = new PacketWriter();
            w.Write4(0);
            w.Write2(1000);
            w.WriteStringWithLength(account);
            w.WriteStringWithLength(password);
            var randStr = "YHERO";
            var time = DateTime.Now.ToString("yyyyMMddHHmmss");
            w.WriteStringWithLength(randStr + time);
            w.Write2(10);
            return w.GetBytes();
        }

        public static byte[] BuildServerListResponse(List<ServerCluster> serverClusters)
        {
            using var w = new PacketWriter();

            // Số lượng cluster
            w.Write2(serverClusters.Count);
            //for (var cluster in serverClusters)
            for (int i = 0;i< serverClusters.Count;i++)
            {
                var cluster = serverClusters[i];
                // ID của cluster
                w.Write2(cluster.ID);
                w.WriteStringWithLength(cluster.ClusterName);
                w.Write4(1);
                w.Write2(1);
                w.Write2(cluster.Channels.Count);
                w.Write2(111); // Bage 111 = new hot pk 
                foreach (var channel in cluster.Channels)
                {
                    w.Write2(channel.ServerID);
                    w.WriteStringWithLength(channel.ServerName);
                    w.Write2(channel.Status ? (short)channel.CurrentPlayers * 100/channel.MaximumOnline : (short)101); // server Status // Ratio % // 101 = offline
                    w.Write2(0x17); // allow pk
                }
            }
            return w.GetBytes();
        }
        
        public static byte[] BuildSelectServerReponse(int clusterId,ServerChannel channel)
        {
            Logger.Instance.Info($"BuildSelectServerReponse: {channel.ServerIP} - {channel.GameServerPort} - {clusterId} - {channel.ServerID} - {channel.ServerName}");
            using var w = new PacketWriter();
            w.WriteStringWithLength(channel.ServerIP);
            w.Write4(channel.GameServerPort);
            w.Write4(clusterId);
            w.Write4(channel.ServerID);
            w.WriteStringWithLength(channel.ServerName);
            return w.GetBytes();
        }
    }
}
