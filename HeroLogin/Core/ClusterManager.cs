
using Grpc.Net.Client;
using HeroLogin.Protos;
using Grpc.Core;

namespace HeroLogin.Core;

public class ClusterManager
{
    private static ClusterManager? _instance;
    private readonly Dictionary<(int, int), ServerChannelExtend> _clusters = new();

    public static ClusterManager Instance => _instance ??= new ClusterManager();

    private ClusterManager()
    {
        // Khởi tạo danh sách cluster từ cấu hình
        var clusterList = ConfigManager.Instance.ServerClusterSettings;
        foreach (var cluster in clusterList)
        {
            foreach (var channel in cluster.Channels)
            {
                var channelExtend = new ServerChannelExtend
                {
                    ServerName = channel.ServerName,
                    ServerID = channel.ServerID,
                    ServerIP = channel.ServerIP,
                    GameServerPort = channel.GameServerPort,
                    GameServerGrpcPort = channel.GameServerGrpcPort,
                    MaximumOnline = channel.MaximumOnline,
                    AutomaticConnectionTime = channel.AutomaticConnectionTime,
                    Status = false,
                    Badge = channel.Badge
                };

                _clusters[(cluster.ID, channel.ServerID)] = channelExtend;
            }
        }
    }

    public ServerChannelExtend? GetServerChannel(int clusterId, int serverId)
    {
        return _clusters.TryGetValue((clusterId, serverId), out var channel) ? channel : null;
    }

    public List<ServerCluster> GetAllClusters()
    {
        var clusters = new List<ServerCluster>();
        var clusterGroups = _clusters.GroupBy(kvp => kvp.Key.Item1);

        foreach (var clusterGroup in clusterGroups)
        {
            var clusterId = clusterGroup.Key;
            var configCluster = ConfigManager.Instance.ServerClusterSettings.FirstOrDefault(c => c.ID == clusterId);

            if (configCluster != null)
            {
                var cluster = new ServerCluster
                {
                    ID = clusterId,
                    ClusterName = configCluster.ClusterName,
                    Channels = new List<ServerChannel>()
                };

                foreach (var kvp in clusterGroup)
                {
                    var serverChannel = kvp.Value;
                    cluster.Channels.Add(serverChannel);
                }

                clusters.Add(cluster);
            }
        }

        return clusters;
    }

    public void UpdateServerChannel(int clusterId, ServerChannelExtend channel)
    {
        _clusters[(clusterId, channel.ServerID)] = channel;
    }

    public void SetGrpcChannel(int clusterId, int serverId, GrpcChannel channel)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            serverChannel.GrpcChannel = channel;
        }
    }

    public GrpcChannel? GetGrpcChannel(int clusterId, int serverId)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            return serverChannel.GrpcChannel;
        }

        return null;
    }

    public void SetTransmitMessageWriter(int clusterId, int serverId, IServerStreamWriter<TransmitMessageResponse> writer)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            serverChannel.TransmitMessageWriter = writer;
        }
    }

    public void RemoveTransmitMessageWriter(int clusterId, int serverId)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            serverChannel.TransmitMessageWriter = null;
        }
    }

    public async Task BroadCastPacketMessage(string message, HashSet<(int clusterId, int serverId)>? excludeServers = null)
    {
        var tasks = new List<Task>();
        int sentCount = 0;

        foreach (var kvp in _clusters)
        {
            var (clusterId, serverId) = kvp.Key;
            var channel = kvp.Value;

            // Bỏ qua server nếu trong danh sách loại trừ
            if (excludeServers?.Contains((clusterId, serverId)) == true)
                continue;

            // Chỉ gửi đến server có TransmitMessageWriter và đang hoạt động
            if (channel.TransmitMessageWriter != null && channel.Status)
            {
                tasks.Add(SendMessageToGameServerAsync(channel, message));
                sentCount++;
            }
        }

        // Chờ tất cả các task hoàn thành
        if (tasks.Count > 0)
        {
            await Task.WhenAll(tasks);
            Console.WriteLine($"[ClusterManager] Đã broadcast message '{message}' đến {sentCount} GameServer(s)");
        }
        else
        {
            Console.WriteLine($"[ClusterManager] Không có GameServer nào để broadcast message '{message}'");
        }
    }

    // Method để test broadcast message


    private static async Task SendMessageToGameServerAsync(ServerChannelExtend channel, string message)
    {
        try
        {
            if (channel.TransmitMessageWriter != null)
            {
                var response = new TransmitMessageResponse
                {
                    Success = true,
                    Message = message
                };

                await channel.TransmitMessageWriter.WriteAsync(response);
            }
        }
        catch
        {
            // Đánh dấu server có vấn đề khi không thể gửi message
            channel.Status = false;

            // Xóa writer bị lỗi
            channel.TransmitMessageWriter = null;
        }
    }
    public IServerStreamWriter<TransmitMessageResponse>? GetTransmitter(int clusterId, int serverId)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            return serverChannel.TransmitMessageWriter;
        }

        return null;
    }
    public async Task SendMessageToOneServer(int clusterId, int serverId, string message)
    {
        if (_clusters.TryGetValue((clusterId, serverId), out var serverChannel))
        {
            if (serverChannel.TransmitMessageWriter != null)
            {
                var response = new TransmitMessageResponse
                {
                    Success = true,
                    Message = message
                };

                await serverChannel.TransmitMessageWriter.WriteAsync(response);
            }
        }
    }

}

public class ServerChannelExtend : ServerChannel
{
    public GrpcChannel? GrpcChannel { get; set; }
    public IServerStreamWriter<TransmitMessageResponse>? TransmitMessageWriter { get; set; }
}