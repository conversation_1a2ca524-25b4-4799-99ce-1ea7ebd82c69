using System;
using System.IO;
using System.Text.Json;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using HeroLogin.Services;

namespace HeroLogin.Core
{
    public class ServerChannel
    {
        public string ServerName { get; set; } = string.Empty;
        public int ServerID { get; set; } = 1;
        public string ServerIP { get; set; } = "127.0.0.1";
        public int GameServerPort { get; set; } = 13000;
        public int GameServerGrpcPort { get; set; } = 7000;
        public int MaximumOnline { get; set; } = 1000;
        public int AutomaticConnectionTime { get; set; } = 60;
        public bool Status { get; set; } = false;
        public int Badge { get; set; } = 0;
        public int CurrentPlayers { get; set; } = 0;
    }

    public class ServerCluster
    {
        public int ID { get; set; } = 1;
        public string ClusterName { get; set; } = string.Empty;
        public List<ServerChannel> Channels { get; set; } = new List<ServerChannel>();
    }

    public class LoginServerSettings
    {
        public string LoginServerIP { get; set; } = "127.0.0.1";
        public int LoginServerGrpcPort { get; set; } = 6999;
        public int LoginServerPort { get; set; } = 1300;
        public int WebSocketPort { get; set; } = 1301;
    }

    public class AppSettings
    {
        public string Environment { get; set; } = "Production";
        public bool ShowPacketData { get; set; } = false;
        public string LogLevel { get; set; } = "Info";
    }

    public class DatabaseConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;

        public string BBGDb { get; set; } = string.Empty;
    }
    public class PogresConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string BBGDb { get; set; } = string.Empty;
    }


    /// <summary>
    /// JSON source generator context for AOT compatibility
    /// </summary>
    [JsonSourceGenerationOptions(WriteIndented = true)]
    [JsonSerializable(typeof(ConfigRoot))]
    [JsonSerializable(typeof(DatabaseConfig))]
    [JsonSerializable(typeof(PogresConfig))]
    [JsonSerializable(typeof(List<ServerCluster>))]
    [JsonSerializable(typeof(ServerCluster))]
    [JsonSerializable(typeof(List<ServerChannel>))]
    [JsonSerializable(typeof(ServerChannel))]
    [JsonSerializable(typeof(LoginServerSettings))]
    [JsonSerializable(typeof(AppSettings))]
    public partial class ConfigJsonContext : JsonSerializerContext
    {
    }

    /// <summary>
    /// Root configuration object for JSON deserialization
    /// </summary>
    public class ConfigRoot
    {
        public DatabaseConfig? ConnectionStrings { get; set; }
        public PogresConfig? PostgresConnectionStrings { get; set; }
        public List<ServerCluster>? ServerClusterSettings { get; set; }
        public LoginServerSettings? LoginServerSettings { get; set; }
        public AppSettings? AppSettings { get; set; }
    }

    public class ConfigManager
    {
        private static ConfigManager? _instance;
        private readonly string _configPath;

        public DatabaseConfig ConnectionStrings { get; private set; }
        public List<ServerCluster> ServerClusterSettings { get; private set; }
        public PogresConfig PogresSettings { get; private set; }
        public LoginServerSettings LoginServerSettings { get; private set; }
        public AppSettings AppSettings { get; private set; }

        public static ConfigManager Instance => _instance ??= new ConfigManager();

        private ConfigManager()
        {
            _configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

            // Khởi tạo cấu hình mặc định
            ConnectionStrings = new DatabaseConfig();
            ServerClusterSettings = new List<ServerCluster>();
            PogresSettings = new PogresConfig();
            LoginServerSettings = new LoginServerSettings();
            AppSettings = new AppSettings();

            LoadConfiguration();
        }

        private void LoadConfiguration()
        {
            try
            {
                if (!File.Exists(_configPath))
                {
                    Logger.Instance.Error($"Không tìm thấy file cấu hình: {_configPath}");
                    Logger.Instance.Error($"Đường dẫn đầy đủ: {Path.GetFullPath(_configPath)}");
                    Logger.Instance.Error($"Thư mục hiện tại: {Directory.GetCurrentDirectory()}");
                    return;
                }

                string jsonContent = File.ReadAllText(_configPath);

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Logger.Instance.Error($"File cấu hình {_configPath} rỗng hoặc chỉ chứa khoảng trắng");
                    return;
                }

                // Sử dụng source generator cho AOT compatibility
                var configRoot = JsonSerializer.Deserialize<ConfigRoot>(jsonContent, ConfigJsonContext.Default.ConfigRoot);

                if (configRoot == null)
                {
                    Logger.Instance.Error("Không thể deserialize file cấu hình");
                    return;
                }

                // Áp dụng cấu hình
                if (configRoot.ConnectionStrings != null)
                {
                    ConnectionStrings = configRoot.ConnectionStrings;
                }

                if (configRoot.PostgresConnectionStrings != null)
                {
                    PogresSettings = configRoot.PostgresConnectionStrings;
                }

                if (configRoot.ServerClusterSettings != null)
                {
                    ServerClusterSettings = configRoot.ServerClusterSettings;
                }

                if (configRoot.LoginServerSettings != null)
                {
                    LoginServerSettings = configRoot.LoginServerSettings;
                }

                if (configRoot.AppSettings != null)
                {
                    AppSettings = configRoot.AppSettings;
                }

                Logger.Instance.Info("Đã tải cấu hình từ appsettings.json thành công");
            }
            catch (JsonException jsonEx)
            {
                Logger.Instance.Error($"File cấu hình {_configPath} không đúng định dạng JSON: {jsonEx.Message}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải cấu hình: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
            }
        }

        public void SaveConfig()
        {
            try
            {
                var configRoot = new ConfigRoot
                {
                    ConnectionStrings = ConnectionStrings,
                    PostgresConnectionStrings = PogresSettings,
                    ServerClusterSettings = ServerClusterSettings,
                    LoginServerSettings = LoginServerSettings,
                    AppSettings = AppSettings
                };

                string json = JsonSerializer.Serialize(configRoot, ConfigJsonContext.Default.ConfigRoot);

                File.WriteAllText(_configPath, json);
                Logger.Instance.Info("Đã lưu cấu hình vào appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu cấu hình: {ex.Message}");
            }
        }
    }
}
