-- <PERSON><PERSON><PERSON> stored procedure nếu đã tồn tại
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RESET_GROUP_QUESTS')
    DROP PROCEDURE [dbo].[SP_RESET_GROUP_QUESTS];
GO

-- <PERSON><PERSON><PERSON> các bảng nếu đã tồn tại (theo thứ tự ngược lại để tránh lỗi ràng buộc khóa ngoại)
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION_LOG]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION_LOG];

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_HISTORY]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_GROUP_QUEST_HISTORY];

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION];

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_FACTION_QUEST_PROGRESS]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_FACTION_QUEST_PROGRESS];

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GUILD_QUEST_PROGRESS]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_GUILD_QUEST_PROGRESS];

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST]') AND type in (N'U'))
    DROP TABLE [dbo].[TBL_GROUP_QUEST];
GO

-- Tạo bảng định nghĩa quest nhóm
CREATE TABLE [dbo].[TBL_GROUP_QUEST] (
    [ID] INT NOT NULL PRIMARY KEY,
    [QuestName] NVARCHAR(100) NOT NULL,
    [QuestDesc] NVARCHAR(500) NOT NULL,
    [QuestType] INT NOT NULL, -- 1: Guild, 2: Faction (FLD_XZ)
    [TargetType] INT NOT NULL, -- 1: Kill Players, 2: Kill Monsters, 3: Kill Boss
    [TargetID] INT NULL, -- ID của monster/boss cụ thể, NULL nếu là kill player
    [TargetCount] INT NOT NULL, -- Số lượng cần tiêu diệt
    [RequiredLevel] INT NOT NULL DEFAULT 0, -- Cấp độ yêu cầu
    [RewardExp] INT NOT NULL DEFAULT 0, -- Kinh nghiệm thưởng
    [RewardMoney] BIGINT NOT NULL DEFAULT 0, -- Tiền thưởng
    [RewardItem] INT NULL, -- ID vật phẩm thưởng
    [RewardItemCount] INT NULL, -- Số lượng vật phẩm
    [ResetType] INT NOT NULL, -- 1: Hàng ngày, 2: Hàng tuần, 3: Hàng tháng, 4: Không reset
    [IsActive] BIT NOT NULL DEFAULT 1, -- Trạng thái kích hoạt
    [CreateDate] DATETIME NOT NULL DEFAULT GETDATE()
);

-- Tạo bảng lưu trữ tiến trình quest của guild
CREATE TABLE [dbo].[TBL_GUILD_QUEST_PROGRESS] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [GuildID] INT NOT NULL,
    [QuestID] INT NOT NULL,
    [CurrentCount] INT NOT NULL DEFAULT 0,
    [Status] TINYINT NOT NULL DEFAULT 1, -- 1: Đã nhận, 2: Đang thực hiện, 3: Đã hoàn thành, 4: Đã hủy
    [AcceptedTime] DATETIME NOT NULL DEFAULT GETDATE(), -- Thời điểm nhận nhiệm vụ
    [LastUpdateTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [CompletedTime] DATETIME NULL, -- Thời điểm hoàn thành
    [CancelledTime] DATETIME NULL, -- Thời điểm hủy
    [LastResetTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [LastCompletedTime] DATETIME NULL, -- Thời điểm hoàn thành cuối cùng
    CONSTRAINT [FK_GUILD_QUEST_PROGRESS_QUEST] FOREIGN KEY ([QuestID]) REFERENCES [dbo].[TBL_GROUP_QUEST] ([ID])
);

-- Thêm ràng buộc để đảm bảo chỉ có một quest với cùng questId ở trạng thái Accept/Processing
CREATE UNIQUE NONCLUSTERED INDEX [UQ_GUILD_QUEST_ACTIVE] ON [dbo].[TBL_GUILD_QUEST_PROGRESS]
(
    [GuildID] ASC,
    [QuestID] ASC
)
WHERE ([Status] IN (1, 2));

-- Tạo bảng lưu trữ tiến trình quest của phe phái (FLD_XZ)
CREATE TABLE [dbo].[TBL_FACTION_QUEST_PROGRESS] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [FactionID] INT NOT NULL, -- 1: Chính, 2: Tà
    [QuestID] INT NOT NULL,
    [CurrentCount] INT NOT NULL DEFAULT 0,
    [Status] TINYINT NOT NULL DEFAULT 1, -- 1: Đã nhận, 2: Đang thực hiện, 3: Đã hoàn thành, 4: Đã hủy
    [AcceptedTime] DATETIME NOT NULL DEFAULT GETDATE(), -- Thời điểm nhận nhiệm vụ
    [LastUpdateTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [CompletedTime] DATETIME NULL, -- Thời điểm hoàn thành
    [CancelledTime] DATETIME NULL, -- Thời điểm hủy
    [LastResetTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [LastCompletedTime] DATETIME NULL, -- Thời điểm hoàn thành cuối cùng
    CONSTRAINT [FK_FACTION_QUEST_PROGRESS_QUEST] FOREIGN KEY ([QuestID]) REFERENCES [dbo].[TBL_GROUP_QUEST] ([ID])
);

-- Thêm ràng buộc để đảm bảo chỉ có một quest với cùng questId ở trạng thái Accept/Processing
CREATE UNIQUE NONCLUSTERED INDEX [UQ_FACTION_QUEST_ACTIVE] ON [dbo].[TBL_FACTION_QUEST_PROGRESS]
(
    [FactionID] ASC,
    [QuestID] ASC
)
WHERE ([Status] IN (1, 2));

-- Tạo bảng lưu trữ đóng góp của người chơi vào quest
CREATE TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [QuestID] INT NOT NULL,
    [ProgressID] INT NOT NULL, -- ID của progress
    [PlayerID] INT NOT NULL,
    [PlayerName] NVARCHAR(50) NOT NULL,
    [GuildID] INT NULL,
    [FactionID] INT NULL,
    [ActionType] INT NOT NULL DEFAULT 1, -- 1: Kill, 2: Collect, etc.
    [TargetID] INT NULL, -- ID của mục tiêu (monster, player, etc.)
    [TargetName] NVARCHAR(50) NULL, -- Tên của mục tiêu
    [ContributionCount] INT NOT NULL DEFAULT 0,
    [ContributionTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [HasReceivedReward] BIT NOT NULL DEFAULT 0,
    CONSTRAINT [FK_GROUP_QUEST_CONTRIBUTION_QUEST] FOREIGN KEY ([QuestID]) REFERENCES [dbo].[TBL_GROUP_QUEST] ([ID])
);

-- Tạo bảng lưu trữ log contribute
CREATE TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION_LOG] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [ContributionID] INT NOT NULL,
    [ProgressID] INT NOT NULL,
    [PlayerID] INT NOT NULL,
    [PlayerName] NVARCHAR(50) NOT NULL,
    [ActionType] INT NOT NULL DEFAULT 1, -- 1: Kill, 2: Collect, etc.
    [TargetID] INT NULL, -- ID của mục tiêu (monster, player, etc.)
    [TargetName] NVARCHAR(50) NULL, -- Tên của mục tiêu
    [ContributionCount] INT NOT NULL DEFAULT 0,
    [ContributionTime] DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [FK_GROUP_QUEST_CONTRIBUTION_LOG_CONTRIBUTION] FOREIGN KEY ([ContributionID]) REFERENCES [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] ([ID])
);

-- Tạo bảng lưu trữ lịch sử hoàn thành quest
CREATE TABLE [dbo].[TBL_GROUP_QUEST_HISTORY] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [QuestID] INT NOT NULL,
    [GuildID] INT NULL,
    [GuildName] NVARCHAR(50) NULL,
    [FactionID] INT NULL,
    [CompletedTime] DATETIME NOT NULL DEFAULT GETDATE(),
    [CompletedBy] NVARCHAR(50) NULL, -- Người hoàn thành cuối cùng
    CONSTRAINT [FK_GROUP_QUEST_HISTORY_QUEST] FOREIGN KEY ([QuestID]) REFERENCES [dbo].[TBL_GROUP_QUEST] ([ID])
);

-- Tạo stored procedure để reset quest theo loại
CREATE PROCEDURE [dbo].[SP_RESET_GROUP_QUESTS]
AS
BEGIN
    -- Reset daily quests - Dựa trên LastCompletedTime
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 1 -- Daily
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(DAY, gqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ ngày hôm trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 1 -- Daily
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(DAY, fqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ ngày hôm trước

    -- Reset weekly quests
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 2 -- Weekly
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(WEEK, gqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ tuần trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 2 -- Weekly
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(WEEK, fqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ tuần trước

    -- Reset monthly quests
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 3 -- Monthly
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND (DATEDIFF(MONTH, gqp.[LastCompletedTime], GETDATE()) >= 1); -- Đã hoàn thành từ tháng trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 3 -- Monthly
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND (DATEDIFF(MONTH, fqp.[LastCompletedTime], GETDATE()) >= 1); -- Đã hoàn thành từ tháng trước

    -- Đánh dấu các nhiệm vụ đang thực hiện quá hạn
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 3, -- Đánh dấu là đã hoàn thành
        [LastResetTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gqp.[Status] IN (1, 2) -- Đang nhận hoặc đang thực hiện
    AND (
        (gq.[ResetType] = 1 AND DATEDIFF(DAY, gqp.[AcceptedTime], GETDATE()) >= 1) OR -- Daily
        (gq.[ResetType] = 2 AND DATEDIFF(WEEK, gqp.[AcceptedTime], GETDATE()) >= 1) OR -- Weekly
        (gq.[ResetType] = 3 AND DATEDIFF(MONTH, gqp.[AcceptedTime], GETDATE()) >= 1) -- Monthly
    );

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 3, -- Đánh dấu là đã hoàn thành
        [LastResetTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE fqp.[Status] IN (1, 2) -- Đang nhận hoặc đang thực hiện
    AND (
        (gq.[ResetType] = 1 AND DATEDIFF(DAY, fqp.[AcceptedTime], GETDATE()) >= 1) OR -- Daily
        (gq.[ResetType] = 2 AND DATEDIFF(WEEK, fqp.[AcceptedTime], GETDATE()) >= 1) OR -- Weekly
        (gq.[ResetType] = 3 AND DATEDIFF(MONTH, fqp.[AcceptedTime], GETDATE()) >= 1) -- Monthly
    );
END
GO