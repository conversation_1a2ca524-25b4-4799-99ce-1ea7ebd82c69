-- <PERSON><PERSON><PERSON> xóa ràng buộc unique index UQ_GUILD_QUEST_ACTIVE
-- Tạo bởi: Augment Agent
-- Ngày: 2025-05-04

-- 1. <PERSON><PERSON><PERSON> ràng buộc unique index UQ_GUILD_QUEST_ACTIVE trên bảng TBL_GUILD_QUEST_PROGRESS
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_GUILD_QUEST_ACTIVE' AND object_id = OBJECT_ID(N'[dbo].[TBL_GUILD_QUEST_PROGRESS]'))
BEGIN
    DROP INDEX [UQ_GUILD_QUEST_ACTIVE] ON [dbo].[TBL_GUILD_QUEST_PROGRESS];
    PRINT 'Đã xóa ràng buộc UQ_GUILD_QUEST_ACTIVE trên bảng TBL_GUILD_QUEST_PROGRESS';
END
ELSE
BEGIN
    PRINT 'Ràng buộc UQ_GUILD_QUEST_ACTIVE không tồn tại trên bảng TBL_GUILD_QUEST_PROGRESS';
END

-- 2. Xóa ràng buộc unique index UQ_FACTION_QUEST_ACTIVE trên bảng TBL_FACTION_QUEST_PROGRESS
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'UQ_FACTION_QUEST_ACTIVE' AND object_id = OBJECT_ID(N'[dbo].[TBL_FACTION_QUEST_PROGRESS]'))
BEGIN
    DROP INDEX [UQ_FACTION_QUEST_ACTIVE] ON [dbo].[TBL_FACTION_QUEST_PROGRESS];
    PRINT 'Đã xóa ràng buộc UQ_FACTION_QUEST_ACTIVE trên bảng TBL_FACTION_QUEST_PROGRESS';
END
ELSE
BEGIN
    PRINT 'Ràng buộc UQ_FACTION_QUEST_ACTIVE không tồn tại trên bảng TBL_FACTION_QUEST_PROGRESS';
END

PRINT 'Đã hoàn thành việc xóa các ràng buộc unique index';
