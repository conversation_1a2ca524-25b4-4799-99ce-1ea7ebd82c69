-- <PERSON><PERSON><PERSON> cập nhật cấu trúc cơ sở dữ liệu cho hệ thống GroupQuest
-- Tạo bởi: Augment Agent
-- Ngày: 2023-11-15

-- 1. Thêm trường LastCompletedTime vào bảng TBL_GUILD_QUEST_PROGRESS
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GUILD_QUEST_PROGRESS]') AND name = 'LastCompletedTime')
BEGIN
    ALTER TABLE [dbo].[TBL_GUILD_QUEST_PROGRESS] ADD [LastCompletedTime] DATETIME NULL;
    PRINT 'Đã thêm trường LastCompletedTime vào bảng TBL_GUILD_QUEST_PROGRESS';
END

-- 2. Thêm trường LastCompletedTime vào bảng TBL_FACTION_QUEST_PROGRESS
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_FACTION_QUEST_PROGRESS]') AND name = 'LastCompletedTime')
BEGIN
    ALTER TABLE [dbo].[TBL_FACTION_QUEST_PROGRESS] ADD [LastCompletedTime] DATETIME NULL;
    PRINT 'Đã thêm trường LastCompletedTime vào bảng TBL_FACTION_QUEST_PROGRESS';
END

-- 3. Cập nhật bảng TBL_GROUP_QUEST_CONTRIBUTION
-- 3.1. Xóa ràng buộc khóa ngoại hiện tại
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_GROUP_QUEST_CONTRIBUTION_QUEST]') AND parent_object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]'))
BEGIN
    ALTER TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] DROP CONSTRAINT [FK_GROUP_QUEST_CONTRIBUTION_QUEST];
    PRINT 'Đã xóa ràng buộc FK_GROUP_QUEST_CONTRIBUTION_QUEST';
END

-- 3.2. Thêm trường ProgressID vào bảng TBL_GROUP_QUEST_CONTRIBUTION
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]') AND name = 'ProgressID')
BEGIN
    ALTER TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] ADD [ProgressID] INT NULL;
    PRINT 'Đã thêm trường ProgressID vào bảng TBL_GROUP_QUEST_CONTRIBUTION';
END

-- 3.3. Thêm trường ActionType vào bảng TBL_GROUP_QUEST_CONTRIBUTION
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]') AND name = 'ActionType')
BEGIN
    ALTER TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] ADD [ActionType] INT NOT NULL DEFAULT 1; -- 1: Kill, 2: Collect, etc.
    PRINT 'Đã thêm trường ActionType vào bảng TBL_GROUP_QUEST_CONTRIBUTION';
END

-- 3.4. Thêm trường TargetID vào bảng TBL_GROUP_QUEST_CONTRIBUTION
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]') AND name = 'TargetID')
BEGIN
    ALTER TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] ADD [TargetID] INT NULL; -- ID của mục tiêu (monster, player, etc.)
    PRINT 'Đã thêm trường TargetID vào bảng TBL_GROUP_QUEST_CONTRIBUTION';
END

-- 3.5. Thêm trường TargetName vào bảng TBL_GROUP_QUEST_CONTRIBUTION
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION]') AND name = 'TargetName')
BEGIN
    ALTER TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] ADD [TargetName] NVARCHAR(50) NULL; -- Tên của mục tiêu
    PRINT 'Đã thêm trường TargetName vào bảng TBL_GROUP_QUEST_CONTRIBUTION';
END

-- 4. Cập nhật dữ liệu cho trường ProgressID
-- 4.1. Cập nhật ProgressID cho các contribution của guild
UPDATE c
SET c.ProgressID = p.ID
FROM [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] c
INNER JOIN [dbo].[TBL_GUILD_QUEST_PROGRESS] p ON c.QuestID = p.QuestID AND c.GuildID = p.GuildID
WHERE c.GuildID IS NOT NULL AND c.ProgressID IS NULL;
PRINT 'Đã cập nhật ProgressID cho các contribution của guild';

-- 4.2. Cập nhật ProgressID cho các contribution của faction
UPDATE c
SET c.ProgressID = p.ID
FROM [dbo].[TBL_GROUP_QUEST_CONTRIBUTION] c
INNER JOIN [dbo].[TBL_FACTION_QUEST_PROGRESS] p ON c.QuestID = p.QuestID AND c.FactionID = p.FactionID
WHERE c.FactionID IS NOT NULL AND c.ProgressID IS NULL;
PRINT 'Đã cập nhật ProgressID cho các contribution của faction';

-- 5. Tạo bảng mới TBL_GROUP_QUEST_CONTRIBUTION_LOG để lưu log contribute
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TBL_GROUP_QUEST_CONTRIBUTION_LOG]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TBL_GROUP_QUEST_CONTRIBUTION_LOG] (
        [ID] INT IDENTITY(1,1) PRIMARY KEY,
        [ContributionID] INT NOT NULL,
        [ProgressID] INT NOT NULL,
        [PlayerID] INT NOT NULL,
        [PlayerName] NVARCHAR(50) NOT NULL,
        [ActionType] INT NOT NULL DEFAULT 1, -- 1: Kill, 2: Collect, etc.
        [TargetID] INT NULL, -- ID của mục tiêu (monster, player, etc.)
        [TargetName] NVARCHAR(50) NULL, -- Tên của mục tiêu
        [ContributionCount] INT NOT NULL DEFAULT 0,
        [ContributionTime] DATETIME NOT NULL DEFAULT GETDATE()
    );
    PRINT 'Đã tạo bảng TBL_GROUP_QUEST_CONTRIBUTION_LOG';
END

-- 6. Thêm ràng buộc để đảm bảo chỉ có một quest với cùng questId ở trạng thái Accept/Processing
-- 6.1. Thêm ràng buộc cho bảng TBL_GUILD_QUEST_PROGRESS
IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_GUILD_QUEST_ACTIVE' AND parent_object_id = OBJECT_ID(N'[dbo].[TBL_GUILD_QUEST_PROGRESS]'))
BEGIN
    ALTER TABLE [dbo].[TBL_GUILD_QUEST_PROGRESS] ADD CONSTRAINT [UQ_GUILD_QUEST_ACTIVE] UNIQUE NONCLUSTERED 
    (
        [GuildID] ASC,
        [QuestID] ASC
    )
    WHERE ([Status] IN (1, 2));
    PRINT 'Đã thêm ràng buộc UQ_GUILD_QUEST_ACTIVE cho bảng TBL_GUILD_QUEST_PROGRESS';
END

-- 6.2. Thêm ràng buộc cho bảng TBL_FACTION_QUEST_PROGRESS
IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_FACTION_QUEST_ACTIVE' AND parent_object_id = OBJECT_ID(N'[dbo].[TBL_FACTION_QUEST_PROGRESS]'))
BEGIN
    ALTER TABLE [dbo].[TBL_FACTION_QUEST_PROGRESS] ADD CONSTRAINT [UQ_FACTION_QUEST_ACTIVE] UNIQUE NONCLUSTERED 
    (
        [FactionID] ASC,
        [QuestID] ASC
    )
    WHERE ([Status] IN (1, 2));
    PRINT 'Đã thêm ràng buộc UQ_FACTION_QUEST_ACTIVE cho bảng TBL_FACTION_QUEST_PROGRESS';
END

-- 7. Cập nhật stored procedure SP_RESET_GROUP_QUESTS để sử dụng LastCompletedTime
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RESET_GROUP_QUESTS')
BEGIN
    DROP PROCEDURE [dbo].[SP_RESET_GROUP_QUESTS];
    PRINT 'Đã xóa stored procedure SP_RESET_GROUP_QUESTS cũ';
END
GO

CREATE PROCEDURE [dbo].[SP_RESET_GROUP_QUESTS]
AS
BEGIN
    -- Reset daily quests - Dựa trên LastCompletedTime
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 1 -- Daily
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(DAY, gqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ ngày hôm trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 1 -- Daily
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(DAY, fqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ ngày hôm trước

    -- Reset weekly quests
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 2 -- Weekly
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(WEEK, gqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ tuần trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 2 -- Weekly
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND DATEDIFF(WEEK, fqp.[LastCompletedTime], GETDATE()) >= 1; -- Đã hoàn thành từ tuần trước

    -- Reset monthly quests
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 3 -- Monthly
    AND gqp.[Status] = 3 -- Đã hoàn thành
    AND gqp.[LastCompletedTime] IS NOT NULL
    AND (DATEDIFF(MONTH, gqp.[LastCompletedTime], GETDATE()) >= 1); -- Đã hoàn thành từ tháng trước

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 1, -- Đánh dấu là đã nhận
        [CurrentCount] = 0,
        [LastResetTime] = GETDATE(),
        [CompletedTime] = NULL,
        [CancelledTime] = NULL,
        [AcceptedTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE gq.[ResetType] = 3 -- Monthly
    AND fqp.[Status] = 3 -- Đã hoàn thành
    AND fqp.[LastCompletedTime] IS NOT NULL
    AND (DATEDIFF(MONTH, fqp.[LastCompletedTime], GETDATE()) >= 1); -- Đã hoàn thành từ tháng trước

    -- Đánh dấu các nhiệm vụ đang thực hiện quá hạn
    UPDATE [dbo].[TBL_GUILD_QUEST_PROGRESS]
    SET [Status] = 3, -- Đánh dấu là đã hoàn thành
        [LastResetTime] = GETDATE()
    FROM [dbo].[TBL_GUILD_QUEST_PROGRESS] gqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON gqp.[QuestID] = gq.[ID]
    WHERE gqp.[Status] IN (1, 2) -- Đang nhận hoặc đang thực hiện
    AND (
        (gq.[ResetType] = 1 AND DATEDIFF(DAY, gqp.[AcceptedTime], GETDATE()) >= 1) OR -- Daily
        (gq.[ResetType] = 2 AND DATEDIFF(WEEK, gqp.[AcceptedTime], GETDATE()) >= 1) OR -- Weekly
        (gq.[ResetType] = 3 AND DATEDIFF(MONTH, gqp.[AcceptedTime], GETDATE()) >= 1) -- Monthly
    );

    UPDATE [dbo].[TBL_FACTION_QUEST_PROGRESS]
    SET [Status] = 3, -- Đánh dấu là đã hoàn thành
        [LastResetTime] = GETDATE()
    FROM [dbo].[TBL_FACTION_QUEST_PROGRESS] fqp
    INNER JOIN [dbo].[TBL_GROUP_QUEST] gq ON fqp.[QuestID] = gq.[ID]
    WHERE fqp.[Status] IN (1, 2) -- Đang nhận hoặc đang thực hiện
    AND (
        (gq.[ResetType] = 1 AND DATEDIFF(DAY, fqp.[AcceptedTime], GETDATE()) >= 1) OR -- Daily
        (gq.[ResetType] = 2 AND DATEDIFF(WEEK, fqp.[AcceptedTime], GETDATE()) >= 1) OR -- Weekly
        (gq.[ResetType] = 3 AND DATEDIFF(MONTH, fqp.[AcceptedTime], GETDATE()) >= 1) -- Monthly
    );
END
GO
PRINT 'Đã tạo stored procedure SP_RESET_GROUP_QUESTS mới';

PRINT 'Cập nhật cấu trúc cơ sở dữ liệu hoàn tất';
