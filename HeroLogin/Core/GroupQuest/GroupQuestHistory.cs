using System;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Class lưu trữ lịch sử hoàn thành quest nhóm
    /// </summary>
    public class GroupQuestHistory
    {
        /// <summary>
        /// ID của bản ghi lịch sử
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// ID của quest
        /// </summary>
        public int QuestID { get; set; }

        /// <summary>
        /// ID của guild (nếu là quest guild)
        /// </summary>
        public int? GuildID { get; set; }

        /// <summary>
        /// Tên của guild (nếu là quest guild)
        /// </summary>
        public string GuildName { get; set; }

        /// <summary>
        /// ID của faction (nếu là quest faction)
        /// </summary>
        public int? FactionID { get; set; }

        /// <summary>
        /// Thời gian hoàn thành
        /// </summary>
        public DateTime CompletedTime { get; set; }

        /// <summary>
        /// Người hoàn thành cuối cùng
        /// </summary>
        public string CompletedBy { get; set; }

        /// <summary>
        /// Thông tin quest
        /// </summary>
        public GroupQuestDefinition QuestDefinition { get; set; }
    }
}
