using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using HeroLogin.Core;
using HeroLogin.Services;
using HeroLogin.Database.FreeSql;
using HeroLogin.Database.FreeSql.Entities.Game;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Lớp quản lý chính cho hệ thống quest nhóm
    /// </summary>
    public class GroupQuestManager
    {
        private static GroupQuestManager _instance;
        private static readonly object _lockObject = new();

        // Danh sách các quest được định nghĩa
        private Dictionary<int, GroupQuestDefinition> _questDefinitions = new();

        // Danh sách tiến trình quest của guild
        public Dictionary<int, List<GroupQuestProgress>> _guildQuestProgress = new();

        // Danh sách tiến trình quest của faction
        public Dictionary<int, List<GroupQuestProgress>> _factionQuestProgress = new();

        // Timer để tự động reset quest
        private System.Timers.Timer _resetTimer;

        // Timer để tự động lưu dữ liệu
        private System.Timers.Timer _saveTimer;

        // Danh sách các tiến trình quest cần lưu
        private HashSet<GroupQuestProgress> _pendingGuildQuestSaves = new();
        private HashSet<GroupQuestProgress> _pendingFactionQuestSaves = new();

        // Danh sách các đóng góp quest cần lưu
        private HashSet<GroupQuestContribution> _pendingGuildContributionSaves = new();
        private HashSet<GroupQuestContribution> _pendingFactionContributionSaves = new();

        // Lock object cho các danh sách pending
        private readonly object _pendingGuildQuestLock = new();
        private readonly object _pendingFactionQuestLock = new();
        private readonly object _pendingGuildContributionLock = new();
        private readonly object _pendingFactionContributionLock = new();

        /// <summary>
        /// Singleton instance
        /// </summary>
        public static GroupQuestManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new GroupQuestManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Constructor
        /// </summary>
        private GroupQuestManager()
        {

            // Khởi tạo timer lưu dữ liệu (mỗi 5 phút)
            _saveTimer = new System.Timers.Timer(1000); // 5 phút = 300000 milliseconds
            _saveTimer.Elapsed += SavePendingData;
            _saveTimer.AutoReset = true;
            _saveTimer.Enabled = true;
        }

        /// <summary>
        /// Khởi tạo hệ thống quest
        /// </summary>
        public void Initialize()
        {
            try
            {
                Logger.Instance.Info( "Đang khởi tạo hệ thống Group Quest...");

                // Load tất cả quest từ database
                LoadAllQuestDefinitions();

                // Load tiến trình quest của guild
                LoadAllGuildQuestProgress();

                // Load tiến trình quest của faction
                LoadAllFactionQuestProgress();

                Logger.Instance.Info( $"Đã khởi tạo hệ thống Group Quest thành công: {_questDefinitions.Count} quest, {_guildQuestProgress.Count} guild progress, {_factionQuestProgress.Count} faction progress");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi khởi tạo hệ thống Group Quest: {ex.Message}");
            }
        }

        #region Quest Definition Methods

        /// <summary>
        /// Load tất cả quest từ database
        /// </summary>
        private void LoadAllQuestDefinitions()
        {
            try
            {
                
                //var dt = DatabaseManager.Instance.GameDb.TblGroupQuests.Where(x => x.IsActive == true).ToList();
                var dt = GameDb.LoadQuestDefinitions();
                if (dt != null && dt.Count > 0)
                {
                    _questDefinitions.Clear();

                    foreach (var row in dt)
                    {
                        GroupQuestDefinition quest = new()
                        {
                            ID = Convert.ToInt32(row.id),
                            QuestName = Convert.ToString(row.questname),
                            QuestDesc = Convert.ToString(row.questdesc),
                            QuestType = (GroupQuestType)Convert.ToInt32(row.questtype),
                            TargetType = (TargetType)Convert.ToInt32(row.targettype),
                            TargetID = row.targetid,
                            TargetCount = Convert.ToInt32(row.targetcount),
                            RequiredLevel = Convert.ToInt32(row.requiredlevel),
                            RewardExp = Convert.ToInt32(row.rewardexp),
                            RewardMoney = Convert.ToInt64(row.rewardmoney),
                            RewardItem = Convert.ToInt32(row.rewarditem),
                            RewardItemCount = row.rewarditemcount,
                            ResetType = (ResetType)Convert.ToInt32(row.resettype),
                            IsActive = Convert.ToBoolean(row.isactive),
                            CreateDate = Convert.ToDateTime(row.createdate)
                        };

                        _questDefinitions[quest.ID] = quest;
                    }

                    Logger.Instance.Info($"Đã load {_questDefinitions.Count} quest từ database");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi load quest từ database: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy quest theo ID
        /// </summary>
        public GroupQuestDefinition GetQuestDefinition(int questId)
        {
            if (_questDefinitions.TryGetValue(questId, out var quest))
            {
                return quest;
            }
            return null;
        }

        /// <summary>
        /// Lấy tất cả quest
        /// </summary>
        public List<GroupQuestDefinition> GetAllQuestDefinitions()
        {
            return _questDefinitions.Values.ToList();
        }

        /// <summary>
        /// Lấy tất cả quest của guild
        /// </summary>
        public List<GroupQuestDefinition> GetGuildQuestDefinitions()
        {
            return _questDefinitions.Values.Where(q => q.QuestType == GroupQuestType.Guild).ToList();
        }

        /// <summary>
        /// Lấy tất cả quest của faction
        /// </summary>
        public List<GroupQuestDefinition> GetFactionQuestDefinitions()
        {
            return _questDefinitions.Values.Where(q => q.QuestType == GroupQuestType.Faction).ToList();
        }

        /// <summary>
        /// Thêm quest mới
        /// </summary>
        public async Task<int> AddQuestDefinition(GroupQuestDefinition quest)
        {
            try
            {
                // Kiểm tra xem ID đã được cung cấp chưa
                if (quest.ID <= 0)
                {
                    Logger.Instance.Info( "Lỗi: ID quest phải được cung cấp và lớn hơn 0");
                    return -1;
                }

                // select Count
                var questCount =  await GameDb.FindGroupQuestId(quest.ID);

                if (questCount > 0)
                {
                    Logger.Instance.Info($"Lỗi: Quest ID {quest.ID} đã tồn tại trong database");
                    return -1;
                }
                var newQuest = new tbl_group_quest
                {
                    id = quest.ID,
                    questname = quest.QuestName,
                    questdesc = quest.QuestDesc,
                    questtype = (int)quest.QuestType,
                    targettype = (int)quest.TargetType,
                    targetid = quest.TargetID,
                    targetcount = quest.TargetCount,
                    requiredlevel = quest.RequiredLevel,
                    rewardexp = quest.RewardExp,
                    rewardmoney = quest.RewardMoney,
                    rewarditem = quest.RewardItem,
                    rewarditemcount = quest.RewardItemCount,
                    resettype = (int)quest.ResetType,
                    isactive = quest.IsActive,
                    createdate = DateTime.Now
                };

                await GameDb.AddGroupQuest(newQuest);

                if (newQuest.id > 0)
                {
                    quest.CreateDate = DateTime.Now;
                    _questDefinitions[quest.ID] = quest;

                    Logger.Instance.Info( $"Đã thêm quest mới: {quest.QuestName} (ID: {quest.ID})");
                    return quest.ID;
                }

                return -1;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi thêm quest mới: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Cập nhật quest
        /// </summary>
        public async Task<bool> UpdateQuestDefinition(GroupQuestDefinition quest)
        {
            try
            {
                var result = await GameDb.UpdateGroupQuest(new tbl_group_quest
                {
                    id = quest.ID,
                    questname = quest.QuestName,
                    questdesc = quest.QuestDesc,
                    questtype = (int)quest.QuestType,
                    targettype = (int)quest.TargetType,
                    targetid = quest.TargetID,
                    targetcount = quest.TargetCount,
                    requiredlevel = quest.RequiredLevel,
                    rewardexp = quest.RewardExp,
                    rewardmoney = quest.RewardMoney,
                    rewarditem = quest.RewardItem,
                    rewarditemcount = quest.RewardItemCount,
                    resettype = (int)quest.ResetType,
                    isactive = quest.IsActive
                }); 
                if (result > 0)
                {
                    _questDefinitions[quest.ID] = quest;
                    Logger.Instance.Info($"Đã cập nhật quest: {quest.QuestName} (ID: {quest.ID})");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi cập nhật quest: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Guild Quest Progress Methods

        /// <summary>
        /// Load tất cả tiến trình quest của guild: nhiệm vụ chưa hoàn thành và nhiệm vụ đã hoàn thành trong ngày hiện tại
        /// </summary>
        private async void LoadAllGuildQuestProgress()
        {
            try
            {
                // Lấy tất cả nhiệm vụ chưa hoàn thành (Status = 1 hoặc 2) và nhiệm vụ đã hoàn thành trong ngày hiện tại
                // string query = @"
                //     SELECT gqp.*, gq.*
                //     FROM TBL_GUILD_QUEST_PROGRESS gqp
                //     INNER JOIN TBL_GROUP_QUEST gq ON gqp.QuestID = gq.ID
                //     WHERE gq.IsActive = 1
                //     AND gqp.Status != 4
                //     AND (
                //         gqp.Status IN (1, 2)
                //         OR (gqp.Status = 3 AND CONVERT(date, gqp.CompletedTime) = CONVERT(date, GETDATE()))
                //     )";

                // DataTable dt = DBA.GetDBToDataTable(query, "heroGame");

                //var dt = DatabaseManager.Instance.GameDb.TblGuildQuestProgresses.Where(x => x.Status != 4 && (x.Status == 1 || x.Status == 2 || (x.Status == 3 && x.CompletedTime.Value.Date == DateTime.Now.Date))).ToList();
                var dt = await GameDb.LoadGuildQuestProcessToday();
                if (dt != null && dt.Count > 0)
                {
                    _guildQuestProgress.Clear();

                    foreach (var row in dt)
                    {
                        int guildId = Convert.ToInt32(row.guildid);
                        int questId = Convert.ToInt32(row.questid);
                        int status = Convert.ToInt32(row.status);

                        // Tạo quest progress
                        GroupQuestProgress progress = new()
                        {
                            ID = Convert.ToInt32(row.id),
                            QuestID = questId,
                            GuildID = guildId,
                            CurrentCount = Convert.ToInt32(row.currentcount),
                            Status = (QuestStatus)status,
                            AcceptedTime = Convert.ToDateTime(row.acceptedtime),
                            LastUpdateTime = Convert.ToDateTime(row.lastupdatetime),
                            CompletedTime = row.completedtime.HasValue ? Convert.ToDateTime(row.completedtime) : null,
                            CancelledTime = row.cancelledtime.HasValue ? Convert.ToDateTime(row.cancelledtime) : null,
                            LastResetTime = Convert.ToDateTime(row.lastresettime),
                            LastCompletedTime = row.lastcompletedtime.HasValue ? Convert.ToDateTime(row.lastcompletedtime) : null
                        };

                        // Lấy thông tin quest
                        if (_questDefinitions.TryGetValue(questId, out var questDef))
                        {
                            progress.QuestDefinition = questDef;
                        }

                        // Thêm vào dictionary
                        if (!_guildQuestProgress.ContainsKey(guildId))
                        {
                            _guildQuestProgress[guildId] = new List<GroupQuestProgress>();
                        }

                        _guildQuestProgress[guildId].Add(progress);

                        // Log thông tin
                        string statusText = status == 1 ? "Đã nhận" : (status == 2 ? "Đang thực hiện" : "Đã hoàn thành");
                    }

                    // Load đóng góp của người chơi
                    LoadAllGuildQuestContributions();

                    Logger.Instance.Info($"Đã load tiến trình quest của {_guildQuestProgress.Count} guild");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi load tiến trình quest guild: {ex.Message}");
            }
        }

        /// <summary>
        /// Load tất cả đóng góp quest của guild
        /// </summary>
        private async void LoadAllGuildQuestContributions()
        {
            try
            {
                // string query = @"
                //     SELECT * FROM TBL_GROUP_QUEST_CONTRIBUTION
                //     WHERE GuildID IS NOT NULL";

                // DataTable dt = DBA.GetDBToDataTable(query, "heroGame");
                //var dt = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Where(x => x.GuildId != null).ToList();
                var dt = await GameDb.LoadAllGroupQuestContributions();
                if (dt != null && dt.Count > 0)
                {
                    foreach (var row in dt)
                    {
                        int guildId = Convert.ToInt32(row.guildid);
                        int questId = Convert.ToInt32(row.questid);

                        // Tìm quest progress
                        if (_guildQuestProgress.TryGetValue(guildId, out var guildProgress))
                        {
                            var progress = guildProgress.FirstOrDefault(p => p.QuestID == questId);

                            if (progress != null)
                            {
                                // Tạo contribution
                                GroupQuestContribution contribution = new()
                                {
                                    ID = Convert.ToInt32(row.id),
                                    QuestID = questId,
                                    PlayerID = Convert.ToInt32(row.playerid),
                                    PlayerName = Convert.ToString(row.playername),
                                    GuildID = guildId,
                                    ContributionCount = Convert.ToInt32(row.contributioncount),
                                    ContributionTime = Convert.ToDateTime(row.contributiontime),
                                    HasReceivedReward = Convert.ToBoolean(row.hasreceivedreward)
                                };

                                // Thêm vào danh sách
                                progress.PlayerContributions.Add(contribution);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi load đóng góp quest guild: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy tiến trình quest của guild
        /// </summary>
        public List<GroupQuestProgress> GetGuildQuestProgress(int guildId)
        {
            if (_guildQuestProgress.TryGetValue(guildId, out var progress))
            {
                return progress;
            }
            return new List<GroupQuestProgress>();
        }

        /// <summary>
        /// Lấy tiến trình quest cụ thể của guild
        /// </summary>
        public GroupQuestProgress GetGuildQuestProgress(int guildId, int questId)
        {
            if (_guildQuestProgress.TryGetValue(guildId, out var progress))
            {
                return progress.FirstOrDefault(p => p.QuestID == questId);
            }
            return null;
        }

        /// <summary>
        /// Tạo hoặc lấy tiến trình quest của guild
        /// </summary>
        public async Task<GroupQuestProgress> GetOrCreateGuildQuestProgress(int guildId, int questId)
        {
            // Kiểm tra quest có tồn tại không
            if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Guild)
            {
                Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Guild");
                return null;
            }

            // Kiểm tra tiến trình đã tồn tại trong bộ nhớ chưa
            var progress = GetGuildQuestProgress(guildId, questId);

            if (progress != null)
            {
                // Nếu nhiệm vụ đã hoàn thành (Status = 3), kiểm tra xem có phải trong ngày hôm nay không
                if (progress.Status == QuestStatus.Completed)
                {
                    if (progress.CompletedTime.HasValue && progress.CompletedTime.Value.Date == DateTime.Today)
                    {
                        // Nhiệm vụ đã hoàn thành trong ngày hôm nay, trả về
                        return progress;
                    }
                    else
                    {
                        // Nhiệm vụ đã hoàn thành nhưng không phải trong ngày hôm nay, xóa khỏi bộ nhớ để tạo mới
                        if (_guildQuestProgress.ContainsKey(guildId))
                        {
                            _guildQuestProgress[guildId].Remove(progress);
                        }
                    }
                }
                else
                {
                    // Nhiệm vụ chưa hoàn thành, trả về
                    return progress;
                }
            }

            // Kiểm tra xem quest có tồn tại trong bảng TBL_GROUP_QUEST không
            try
            {
                if (!GameDb.CheckQuestActive(questId))
                {
                    Logger.Instance.Info( $"Quest ID {questId} không tồn tại trong bảng TBL_GROUP_QUEST hoặc không active");
                    return null;
                }

                var existingProgress =  await GameDb.GetGuildQuestProgressToday(guildId, questId);

                if (existingProgress != null)
                {
                    // Đã có bản ghi trong database, tạo đối tượng progress từ dữ liệu này
                    var row = existingProgress;
                    int status = Convert.ToInt32(row.status);

                    progress = new GroupQuestProgress
                    {
                        ID = Convert.ToInt32(row.id),
                        QuestID = questId,
                        GuildID = guildId,
                        CurrentCount = Convert.ToInt32(row.currentcount),
                        Status = (QuestStatus)status,
                        AcceptedTime = Convert.ToDateTime(row.acceptedtime),
                        LastUpdateTime = Convert.ToDateTime(row.lastupdatetime),
                        LastResetTime = Convert.ToDateTime(row.lastresettime),
                        CompletedTime = row.completedtime.HasValue ? Convert.ToDateTime(row.completedtime) : null,
                        CancelledTime = row.cancelledtime.HasValue ? Convert.ToDateTime(row.cancelledtime) : null,
                        LastCompletedTime = row.lastcompletedtime.HasValue ? Convert.ToDateTime(row.lastcompletedtime) : null,
                        QuestDefinition = questDef
                    };

                    // Thêm vào danh sách
                    if (!_guildQuestProgress.ContainsKey(guildId))
                    {
                        _guildQuestProgress[guildId] = new List<GroupQuestProgress>();
                    }
                    _guildQuestProgress[guildId].Add(progress);

                    string statusText = status == 1 ? "Đã nhận" : (status == 2 ? "Đang thực hiện" : "Đã hoàn thành");
                    Logger.Instance.Info( $"Đã tìm thấy nhiệm vụ guild {guildId}, quest {questId}, trạng thái: {statusText}");
                    return progress;
                }

                // Nếu không có bản ghi nào phù hợp, tạo mới
                progress = new GroupQuestProgress
                {
                    QuestID = questId,
                    GuildID = guildId,
                    CurrentCount = 0,
                    Status = QuestStatus.Accepted,
                    AcceptedTime = DateTime.Now,
                    LastUpdateTime = DateTime.Now,
                    LastResetTime = DateTime.Now,
                    QuestDefinition = questDef
                };

                // Lưu vào database
                var result = await GameDb.CreateGuildQuestProgress(new tbl_guild_quest_progress() { guildid = guildId, questid = questId, currentcount = 0, status = 1, acceptedtime = DateTime.Now, lastupdatetime = DateTime.Now, lastresettime = DateTime.Now });
                if (result == null)
                {
                    throw new Exception("Không thể tạo mới tiến trình quest guild");
                }
                progress.ID = (int)result.id;


                Logger.Instance.Info( $"Đã tạo mới nhiệm vụ guild {guildId}, quest {questId} cho ngày hôm nay");

                // Thêm vào danh sách
                if (!_guildQuestProgress.ContainsKey(guildId))
                {
                    _guildQuestProgress[guildId] = new List<GroupQuestProgress>();
                }
                _guildQuestProgress[guildId].Add(progress);
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi tạo tiến trình quest guild: {ex.Message}");
                return null;
            }

            return progress;
        }
        /// <summary>
        /// Cập nhật tiến trình quest của guild
        /// </summary>
        public async Task<bool> UpdateGuildQuestProgress(GroupQuestProgress progress)
        {
            try
            {
              
                var result = await GameDb.UpdateGuildQuestProgress(new tbl_guild_quest_progress() { id = progress.ID, currentcount = progress.CurrentCount, status = (short)progress.Status, lastupdatetime = progress.LastUpdateTime, completedtime = progress.CompletedTime, cancelledtime = progress.CancelledTime });
                if (result == null)
                {
                    throw new Exception("Không thể cập nhật tiến trình quest guild");
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi cập nhật tiến trình quest guild: {ex.Message}");
                return false;
            }
        }


        /// <summary>
        /// Thêm đóng góp của người chơi vào quest guild (phiên bản nhận các tham số trực tiếp)
        /// </summary>
        /// <param name="SessionID">ID của người chơi</param>
        /// <param name="UserName">Tên của người chơi</param>
        /// <param name="questId">ID của quest</param>
        /// <param name="GuildId">ID của guild</param>
        /// <param name="amount">Số lượng đóng góp</param>
        /// <param name="actionType">Loại hành động (1: Kill, 2: Collect, etc.)</param>
        /// <param name="targetID">ID của mục tiêu (monster, player, etc.)</param>
        /// <param name="targetName">Tên của mục tiêu</param>
        /// <returns>Đối tượng đóng góp đã được cập nhật</returns>
        public async Task<GroupQuestContribution> AddGuildQuestContribution(int SessionID, string UserName, GroupQuestProgress quest, int GuildId, int amount = 1, int actionType = 1, int? targetID = null, string targetName = null)
        {
            // Lấy hoặc tạo tiến trình quest
            var progress = await GetOrCreateGuildQuestProgress(GuildId, quest.QuestID);

            if (progress == null || progress.Status == QuestStatus.Completed || progress.Status == QuestStatus.Cancelled)
            {
                return null;
            }

            // Thêm đóng góp với thông tin chi tiết hơn
            var contribution = progress.AddContribution(SessionID, UserName, amount, actionType, targetID, targetName);

            try
            {

                // var existingId = DBA.GetDBToDataTable(checkQuery, checkParams, "heroGame");
                var existingId = await GameDb.FindGuildQuestContribution(quest.QuestID, SessionID, GuildId, progress.ID);

                if (existingId != null)
                {
                    // Đã có bản ghi, chỉ cập nhật ID và đưa vào queue
                    contribution.ID = Convert.ToInt32(existingId.id);

                    // Đưa vào queue để cập nhật sau
                    SaveGuildQuestContribution(contribution, false);
                }
                else
                {
                    // Thêm bản ghi mới - thực hiện ngay lập tức
                    Logger.Instance.Info($"Thêm contribution mới - QuestID={quest.QuestID}, PlayerID={SessionID}, PlayerName={UserName}, GuildID={GuildId}, Count={contribution.ContributionCount}");

                

                    try
                    {
                        // var result = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Add(new TblGroupQuestContribution
                        // {
                        //     QuestId = quest.QuestID,
                        //     ProgressId = progress.ID,
                        //     PlayerId = SessionID,
                        //     PlayerName = UserName,
                        //     GuildId = GuildId,
                        //     ActionType = contribution.ActionType,
                        //     TargetId = contribution.TargetID,
                        //     TargetName = contribution.TargetName,
                        //     ContributionCount = contribution.ContributionCount,
                        //     ContributionTime = contribution.ContributionTime,
                        //     HasReceivedReward = contribution.HasReceivedReward
                        // });
                        // DatabaseManager.Instance.GameDb.SaveChanges();
                        var result = await GameDb.CreateGuildQuestContribution(new tbl_group_quest_contribution() { questid = quest.QuestID, progressid = progress.ID, playerid = SessionID, playername = UserName, guildid = GuildId, actiontype = contribution.ActionType, targetid = contribution.TargetID, targetname = contribution.TargetName, contributioncount = contribution.ContributionCount, contributiontime = contribution.ContributionTime, hasreceivedreward = contribution.HasReceivedReward });
                        if (result == null)
                        {
                            throw new Exception("Không thể thêm mới contribution");
                        }
                        contribution.ID = (int)result.id;

                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Info( $"Lỗi thêm mới contribution: {ex.Message}");
                    }
                }

                // Cập nhật tiến trình quest
               
               

                // Kiểm tra hoàn thành
                if (progress.IncrementCount(amount))
                {
                    progress.CompletedTime = DateTime.Now;
                    // Lưu ngay lập tức khi hoàn thành quest
                    SaveGuildQuestProgress(progress, true);

                    // Thêm vào lịch sử
                    AddQuestHistory(quest.QuestID, GuildId, null, UserName);
                }
                else
                {
                    SaveGuildQuestProgress(progress);
                }
                SendProgressToUser(contribution, quest);
                return contribution;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi thêm đóng góp quest guild: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Hủy nhiệm vụ guild
        /// </summary>
        public async Task<bool> CancelGuildQuest(int guildId, int questId)
        {
            try
            {
                // Lấy tiến trình quest
                var progress = GetGuildQuestProgress(guildId, questId);

                if (progress == null)
                {
                    return false;
                }

                // Chỉ có thể hủy nhiệm vụ đang nhận hoặc đang thực hiện
                if (progress.Status != QuestStatus.Accepted && progress.Status != QuestStatus.InProgress)
                {
                   return false;
                }

                progress.Cancel();

                //// Cập nhật database
                ///
                var success = await UpdateGuildQuestProgress(progress);
                _guildQuestProgress[guildId].Remove(progress);
                return success;
                //return DeleteGuildQuestProgress(guildId, questId);
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi hủy nhiệm vụ guild: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Đánh dấu người chơi đã nhận phần thưởng quest guild
        /// </summary>
        public async Task<bool> MarkGuildQuestRewardReceived(int guildId, int questId, string playerName)
        {
            try
            {
                // Lấy tiến trình quest
                var progress = GetGuildQuestProgress(guildId, questId);

                if (progress == null || !progress.IsCompleted)
                {
                    return false;
                }

                // Tìm đóng góp của người chơi
                var contribution = progress.PlayerContributions.FirstOrDefault(c => c.PlayerName == playerName);

                if (contribution == null)
                {
                    return false;
                }

                // Đánh dấu đã nhận
                contribution.MarkRewardReceived();

                // Cập nhật database
                // string query = @"
                //     UPDATE TBL_GROUP_QUEST_CONTRIBUTION SET
                //     HasReceivedReward = 1
                //     WHERE ID = @ID";

                // SqlParameter[] parameters = new SqlParameter[]
                // {
                //     new("@ID", contribution.ID)
                // };

                // int result = DBA.ExeSqlCommand2(query, parameters);
                
                var result = await GameDb.UpdateGroupQuestContributionRewardReceived(contribution.ID);
                return result;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đánh dấu nhận thưởng quest guild: {ex.Message}");
                return false;
            }
        }




        /// <summary>
        /// Lấy tên guild từ ID
        /// </summary>
        public async Task<string> GetGuildName(int guildId)
        {
            try
            {
                // string query = "SELECT G_Name FROM TBL_XWWL_Guild WHERE ID = @ID";
                // SqlParameter[] parameters = new SqlParameter[] { new("@ID", guildId) };

                // var result = DBA.GetDBToDataTable(query, parameters);
                var res = await GameDb.FindGUildById(guildId);
                if (res != null)
                {
                    return res.g_name;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"Lỗi lấy tên guild: {ex.Message}");
            }

            return string.Empty;
        }


        #endregion

        #region Faction Quest Progress Methods

        /// <summary>
        /// Load tất cả tiến trình quest của faction
        /// </summary>
        private void LoadAllFactionQuestProgress()
        {
            try
            {
                // string query = @"
                //     SELECT fqp.*, gq.*
                //     FROM TBL_FACTION_QUEST_PROGRESS fqp
                //     INNER JOIN TBL_GROUP_QUEST gq ON fqp.QuestID = gq.ID
                //     WHERE gq.IsActive = 1";

                // DataTable dt = DBA.GetDBToDataTable(query, "heroGame");
                // var dt = GameDb.FindFactionQuestProgressNotCompleted();

                // if (dt != null && dt.Count > 0)
                // {
                //     _factionQuestProgress.Clear();

                //     foreach (var row in dt)
                //     {
                //         int factionId = Convert.ToInt32(row.FactionId);
                //         int questId = Convert.ToInt32(row.QuestId);

                //         // Tạo quest progress
                //         GroupQuestProgress progress = new()
                //         {
                //             ID = Convert.ToInt32(row.Id),
                //             QuestID = questId,
                //             FactionID = factionId,
                //             CurrentCount = Convert.ToInt32(row.CurrentCount),
                //             Status = (QuestStatus)Convert.ToInt32(row.Status),
                //             AcceptedTime = Convert.ToDateTime(row.AcceptedTime),
                //             LastUpdateTime = Convert.ToDateTime(row.LastUpdateTime),
                //             CompletedTime = row.CompletedTime != null ? Convert.ToDateTime(row.CompletedTime) : (DateTime?)null,
                //             CancelledTime = row.CancelledTime != null ? Convert.ToDateTime(row.CancelledTime) : (DateTime?)null,
                //             LastResetTime = Convert.ToDateTime(row.LastResetTime)
                //         };

                //         // Lấy thông tin quest
                //         if (_questDefinitions.TryGetValue(questId, out var questDef))
                //         {
                //             progress.QuestDefinition = questDef;
                //         }

                //         // Thêm vào dictionary
                //         if (!_factionQuestProgress.ContainsKey(factionId))
                //         {
                //             _factionQuestProgress[factionId] = new List<GroupQuestProgress>();
                //         }

                //         _factionQuestProgress[factionId].Add(progress);
                //     }

                //     // Load đóng góp của người chơi
                //     LoadAllFactionQuestContributions();

                //     Logger.Instance.Info($"Đã load tiến trình quest của {_factionQuestProgress.Count} faction");
                //}
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi load tiến trình quest faction: {ex.Message}");
            }
        }

        /// <summary>
        /// Load tất cả đóng góp quest của faction
        /// </summary>
        private void LoadAllFactionQuestContributions()
        {
            try
            {
                // string query = @"
                //     SELECT * FROM TBL_GROUP_QUEST_CONTRIBUTION
                //     WHERE FactionID IS NOT NULL";

                // DataTable dt = DBA.GetDBToDataTable(query, "heroGame");
                // var dt = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Where(x => x.FactionId != null).ToList();

                // if (dt != null && dt.Count > 0)
                // {
                //     foreach (var row in dt)
                //     {
                //         int factionId = Convert.ToInt32(row.FactionId);
                //         int questId = Convert.ToInt32(row.QuestId);

                //         // Tìm quest progress
                //         if (_factionQuestProgress.TryGetValue(factionId, out var factionProgress))
                //         {
                //             var progress = factionProgress.FirstOrDefault(p => p.QuestID == questId);

                //             if (progress != null)
                //             {
                //                 // Tạo contribution
                //                 GroupQuestContribution contribution = new()
                //                 {
                //                     ID = Convert.ToInt32(row.Id),
                //                     QuestID = questId,
                //                     PlayerID = Convert.ToInt32(row.PlayerId),
                //                     PlayerName = Convert.ToString(row.PlayerName),
                //                     FactionID = factionId,
                //                     ContributionCount = Convert.ToInt32(row.ContributionCount),
                //                     ContributionTime = Convert.ToDateTime(row.ContributionTime),
                //                     HasReceivedReward = Convert.ToBoolean(row.HasReceivedReward)
                //                 };

                //                 // Thêm vào danh sách
                //                 progress.PlayerContributions.Add(contribution);
                //             }
                //         }
                //     }
                // }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi load đóng góp quest faction: {ex.Message}");
            }
        }
        /// <summary>
        /// Lấy tiến trình quest của faction
        /// </summary>
        public List<GroupQuestProgress> GetFactionQuestProgress(int factionId)
        {
            if (_factionQuestProgress.TryGetValue(factionId, out var progress))
            {
                return progress;
            }
            return new List<GroupQuestProgress>();
        }

        /// <summary>
        /// Lấy tiến trình quest cụ thể của faction
        /// </summary>
        public GroupQuestProgress GetFactionQuestProgress(int factionId, int questId)
        {
            if (_factionQuestProgress.TryGetValue(factionId, out var progress))
            {
                return progress.FirstOrDefault(p => p.QuestID == questId);
            }
            return null;
        }

        /// <summary>
        /// Tạo hoặc lấy tiến trình quest của faction
        /// </summary>
        public GroupQuestProgress GetOrCreateFactionQuestProgress(int factionId, int questId)
        {
            // Kiểm tra quest có tồn tại không
            if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Faction)
            {
                Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Faction");
                return null;
            }

            // Kiểm tra tiến trình đã tồn tại chưa
            var progress = GetFactionQuestProgress(factionId, questId);

            if (progress != null)
            {
                return progress;
            }

            // Kiểm tra xem quest có tồn tại trong bảng TBL_GROUP_QUEST không
            try
            {
                // string checkQuery = "SELECT COUNT(*) FROM TBL_GROUP_QUEST WHERE ID = @QuestID AND IsActive = 1";
                // SqlParameter[] checkParams = new SqlParameter[] { new("@QuestID", questId) };

                // var questCount = DBA.GetDBToDataTable(checkQuery, checkParams);
                // var questCount = DatabaseManager.Instance.GameDb.TblGroupQuests.Where(x => x.Id == questId && x.IsActive).ToList();

                // if (questCount.Count == 0)
                // {
                //     Logger.Instance.Info( $"Quest ID {questId} không tồn tại trong bảng TBL_GROUP_QUEST hoặc không active");
                //     return null;
                // }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi kiểm tra quest trong database: {ex.Message}");
                return null;
            }

            // Tạo mới tiến trình
            progress = new GroupQuestProgress
            {
                QuestID = questId,
                FactionID = factionId,
                CurrentCount = 0,
                Status = QuestStatus.Accepted,
                AcceptedTime = DateTime.Now,
                LastUpdateTime = DateTime.Now,
                LastResetTime = DateTime.Now,
                QuestDefinition = questDef
            };

            // Lưu vào database
            try
            {
                // string query = @"
                //     INSERT INTO TBL_FACTION_QUEST_PROGRESS
                //     (FactionID, QuestID, CurrentCount, Status, AcceptedTime, LastUpdateTime, LastResetTime)
                //     VALUES
                //     (@FactionID, @QuestID, @CurrentCount, @Status, @AcceptedTime, @LastUpdateTime, @LastResetTime);
                //     SELECT SCOPE_IDENTITY();";

                // SqlParameter[] parameters = new SqlParameter[]
                // {
                //     new("@FactionID", factionId),
                //     new("@QuestID", questId),
                //     new("@CurrentCount", progress.CurrentCount),
                //     new("@Status", (int)progress.Status),
                //     new("@AcceptedTime", progress.AcceptedTime),
                //     new("@LastUpdateTime", progress.LastUpdateTime),
                //     new("@LastResetTime", progress.LastResetTime)
                // };

                // DBA.ExeSqlCommand2(query, parameters);
                // var result = DatabaseManager.Instance.GameDb.TblFactionQuestProgresses.Add(new TblFactionQuestProgress
                // {
                //     FactionId = factionId,
                //     QuestId = questId,
                //     CurrentCount = progress.CurrentCount,
                //     Status = (byte)progress.Status,
                //     AcceptedTime = progress.AcceptedTime,
                //     LastUpdateTime = progress.LastUpdateTime,
                //     LastResetTime = progress.LastResetTime
                // });
                // DatabaseManager.Instance.GameDb.SaveChanges();
                // progress.ID = result.Entity.Id; 


            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"Lỗi tạo tiến trình quest faction: {ex.Message}");
                return null;
            }

            return progress;
        }

        /// <summary>
        /// Cập nhật tiến trình quest của faction
        /// </summary>
        // public bool UpdateFactionQuestProgress(GroupQuestProgress progress)
        // {
        //     try
        //     {
        //         // string query = @"
        //         //     UPDATE TBL_FACTION_QUEST_PROGRESS SET
        //         //     CurrentCount = @CurrentCount,
        //         //     Status = @Status,
        //         //     LastUpdateTime = @LastUpdateTime,
        //         //     CompletedTime = @CompletedTime,
        //         //     CancelledTime = @CancelledTime
        //         //     WHERE ID = @ID";

        //         // SqlParameter[] parameters = new SqlParameter[]
        //         // {
        //         //     new("@ID", progress.ID),
        //         //     new("@CurrentCount", progress.CurrentCount),
        //         //     new("@Status", (int)progress.Status),
        //         //     new("@LastUpdateTime", progress.LastUpdateTime),
        //         //     new("@CompletedTime", progress.CompletedTime.HasValue ? progress.CompletedTime.Value : DBNull.Value),
        //         //     new("@CancelledTime", progress.CancelledTime.HasValue ? progress.CancelledTime.Value : DBNull.Value)
        //         // };

        //         // int result = DBA.ExeSqlCommand2(query, parameters);
        //         // var result = DatabaseManager.Instance.GameDb.TblFactionQuestProgresses.Where(x => x.Id == progress.ID).FirstOrDefault();
        //         // if (result != null)
        //         // {
        //         //     result.CurrentCount = progress.CurrentCount;
        //         //     result.Status = (byte)progress.Status;
        //         //     result.LastUpdateTime = progress.LastUpdateTime;
        //         //     result.CompletedTime = progress.CompletedTime;
        //         //     result.CancelledTime = progress.CancelledTime;
        //         //     DatabaseManager.Instance.GameDb.SaveChanges();
        //         // }

        //         // if (result != null)
        //         // {
        //         //     //Logger.Instance.Info( $"Đã cập nhật tiến trình quest faction {progress.FactionID}, quest {progress.QuestID}: {progress.CurrentCount}/{progress.QuestDefinition?.TargetCount}");
        //         //     return true;
        //         // }

        //         // return false;
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi cập nhật tiến trình quest faction: {ex.Message}");
        //         return false;
        //     }
        // }

        // /// <summary>
        // /// Thêm đóng góp của người chơi vào quest faction (phiên bản nhận các tham số trực tiếp)
        // /// </summary>
        // public GroupQuestContribution AddFactionQuestContribution(int playerSessionID, string playerName, int factionId, int questId, int amount = 1)
        // {
        //     // Lấy hoặc tạo tiến trình quest
        //     var progress = GetOrCreateFactionQuestProgress(factionId, questId);

        //     if (progress == null)
        //     {
        //         return null;
        //     }

        //     // Thêm đóng góp
        //     var contribution = progress.AddContribution(playerSessionID, playerName, amount);

        //     // Lưu vào database
        //     try
        //     {
        //         // Kiểm tra xem đã có bản ghi chưa
        //         // string checkQuery = "SELECT ID FROM TBL_GROUP_QUEST_CONTRIBUTION WHERE QuestID = @QuestID AND PlayerID = @PlayerID AND FactionID = @FactionID";
        //         // SqlParameter[] checkParams = new SqlParameter[]
        //         // {
        //         //     new("@QuestID", questId),
        //         //     new("@PlayerID", playerSessionID),
        //         //     new("@FactionID", factionId)
        //         // };

        //         // var existingId = DBA.GetDBToDataTable(checkQuery, checkParams, "heroGame");
        //         var existingId = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Where(x => x.QuestId == questId && x.PlayerId == playerSessionID && x.FactionId == factionId).ToList();

        //         if (existingId != null && existingId.Count > 0)
        //         {
        //             // Đã có bản ghi, chỉ cập nhật ID và đưa vào queue
        //             contribution.ID = Convert.ToInt32(existingId[0].Id);

        //             // Đưa vào queue để cập nhật sau
        //             SaveFactionQuestContribution(contribution, false);
        //             Logger.Instance.Info($"Đã đưa cập nhật faction contribution vào queue: ID={contribution.ID}, QuestID={questId}, PlayerID={playerSessionID}, Count={contribution.ContributionCount}");
        //         }
        //         else
        //         {
        //             // Thêm bản ghi mới - thực hiện ngay lập tức
        //             Logger.Instance.Info($"Thêm faction contribution mới - QuestID={questId}, PlayerID={playerSessionID}, PlayerName={playerName}, FactionID={factionId}, Count={contribution.ContributionCount}");

        //             // string insertQuery = @"
        //             //     INSERT INTO TBL_GROUP_QUEST_CONTRIBUTION
        //             //     (QuestID, PlayerID, PlayerName, FactionID, ContributionCount, ContributionTime, HasReceivedReward)
        //             //     VALUES
        //             //     (@QuestID, @PlayerID, @PlayerName, @FactionID, @ContributionCount, @ContributionTime, @HasReceivedReward);
        //             //     SELECT SCOPE_IDENTITY();";

        //             // SqlParameter[] insertParams = new SqlParameter[]
        //             // {
        //             //     new SqlParameter("@QuestID", SqlDbType.Int) { Value = questId },
        //             //     new SqlParameter("@PlayerID", SqlDbType.Int) { Value = playerSessionID },
        //             //     new SqlParameter("@PlayerName", SqlDbType.NVarChar, 50) { Value = playerName },
        //             //     new SqlParameter("@FactionID", SqlDbType.Int) { Value = factionId },
        //             //     new SqlParameter("@ContributionCount", SqlDbType.Int) { Value = contribution.ContributionCount },
        //             //     new SqlParameter("@ContributionTime", SqlDbType.DateTime) { Value = contribution.ContributionTime },
        //             //     new SqlParameter("@HasReceivedReward", SqlDbType.Bit) { Value = contribution.HasReceivedReward }
        //             // };

        //             try
        //             {
        //                 // DBA.ExeSqlCommand2(insertQuery, insertParams, "heroGame");
        //                 var result = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Add(new TblGroupQuestContribution
        //                 {
        //                     QuestId = questId,
        //                     PlayerId = playerSessionID,
        //                     PlayerName = playerName,
        //                     FactionId = factionId,
        //                     ContributionCount = contribution.ContributionCount,
        //                     ContributionTime = contribution.ContributionTime,
        //                     HasReceivedReward = contribution.HasReceivedReward
        //                 });
        //                 DatabaseManager.Instance.GameDb.SaveChanges();
        //                 contribution.ID = result.Entity.Id;

        //             }
        //             catch (Exception ex)
        //             {
        //                 Logger.Instance.Info($"Lỗi thêm mới faction contribution: {ex.Message}");
        //             }
        //         }

        //         // Cập nhật tiến trình quest
        //         progress.IncrementCount(amount);
        //         // Thêm vào danh sách chờ lưu thay vì lưu ngay lập tức
        //         SaveFactionQuestProgress(progress);


        //         // Kiểm tra hoàn thành
        //         if (progress.IsCompleted && !progress.CompletedTime.HasValue)
        //         {
        //             progress.CompletedTime = DateTime.Now;
        //             // Lưu ngay lập tức khi hoàn thành quest
        //             SaveFactionQuestProgress(progress, true);

        //             // Thêm vào lịch sử
        //             AddQuestHistory(questId, null, factionId, playerName);

        //             // Thông báo hoàn thành
        //             BroadcastFactionQuestCompleted(factionId, questId, playerName);
        //         }

        //         return contribution;
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi thêm đóng góp quest faction: {ex.Message}");
        //         return null;
        //     }
        // }

        // /// <summary>
        // /// Hủy nhiệm vụ faction
        // /// </summary>
        // public bool CancelFactionQuest(int factionId, int questId)
        // {
        //     try
        //     {
        //         // Lấy tiến trình quest
        //         var progress = GetFactionQuestProgress(factionId, questId);

        //         if (progress == null)
        //         {
        //             return false;
        //         }

        //         // Chỉ có thể hủy nhiệm vụ đang nhận hoặc đang thực hiện
        //         if (progress.Status != QuestStatus.Accepted && progress.Status != QuestStatus.InProgress)
        //         {
        //             return false;
        //         }

        //         // Đánh dấu đã hủy
        //         progress.Cancel();

        //         // Cập nhật database
        //         return UpdateFactionQuestProgress(progress);
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi hủy nhiệm vụ faction: {ex.Message}");
        //         return false;
        //     }
        // }

        // /// <summary>
        // /// Đánh dấu người chơi đã nhận phần thưởng quest faction
        // /// </summary>
        // public bool MarkFactionQuestRewardReceived(int factionId, int questId, string playerName)
        // {
        //     try
        //     {
        //         // Lấy tiến trình quest
        //         var progress = GetFactionQuestProgress(factionId, questId);

        //         if (progress == null || !progress.IsCompleted)
        //         {
        //             return false;
        //         }

        //         // Tìm đóng góp của người chơi
        //         var contribution = progress.PlayerContributions.FirstOrDefault(c => c.PlayerName == playerName);

        //         if (contribution == null)
        //         {
        //             return false;
        //         }

        //         // Đánh dấu đã nhận
        //         contribution.MarkRewardReceived();

        //         // // Cập nhật database
        //         // string query = @"
        //         //     UPDATE TBL_GROUP_QUEST_CONTRIBUTION SET
        //         //     HasReceivedReward = 1
        //         //     WHERE ID = @ID";

        //         // SqlParameter[] parameters = new SqlParameter[]
        //         // {
        //         //     new("@ID", contribution.ID)
        //         // };

        //         // int result = DBA.ExeSqlCommand2(query, parameters);
        //                 var result = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Where(x => x.Id == contribution.ID).FirstOrDefault();
        //         if (result != null)
        //         {
        //             result.HasReceivedReward = true;
        //             DatabaseManager.Instance.GameDb.SaveChanges();
        //         }

        //         return result != null;
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi đánh dấu nhận thưởng quest faction: {ex.Message}");
        //         return false;
        //     }
        // }

        // /// <summary>
        // /// Thông báo hoàn thành quest faction
        // /// </summary>
        // private void BroadcastFactionQuestCompleted(int factionId, int questId, string completedBy)
        // {
        //     try
        //     {
        //         // Lấy thông tin quest
        //         var quest = GetQuestDefinition(questId);

        //         if (quest == null)
        //         {
        //             return;
        //         }

        //         // Lấy tên faction
        //         string factionName = factionId == 1 ? "Chính Phái" : "Tà Phái";

        //         // Gửi thông báo toàn server
        //         // string message = $"{factionName} đã hoàn thành nhiệm vụ [{quest.QuestName}]!";
        //         // World.SendBroadCast(message);

        //         // Gửi thông báo cho tất cả thành viên trong faction
        //         string factionMessage = $"Phe phái đã hoàn thành nhiệm vụ [{quest.QuestName}]! Hãy đến nhận thưởng!";
        //         SendMessageToFactionMembers(factionId, factionMessage);
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi thông báo hoàn thành quest faction: {ex.Message}");
        //     }
        // }

        // /// <summary>
        // /// Gửi thông báo cho tất cả thành viên trong faction
        // /// </summary>
        // private void SendMessageToFactionMembers(int factionId, string message)
        // {
        //     try
        //     {
        //         // Gửi thông báo cho tất cả người chơi online thuộc faction
        //         // foreach (var player in World.allConnectedChars.Values)
        //         // {
        //         //     if (player.Player_Zx == factionId)
        //         //     {
        //         //         player.HeThongNhacNho(message, 10, "Nhiệm vụ phe phái");
        //         //     }
        //         // }
        //     }
        //     catch (Exception ex)
        //     {
        //         Logger.Instance.Info( $"Lỗi gửi thông báo cho thành viên faction: {ex.Message}");
        //     }
        // }

        #endregion

        #region Quest History Methods

        /// <summary>
        /// Thêm lịch sử hoàn thành quest
        /// </summary>
        public async void AddQuestHistory(int questId, int guildId, int? factionId, string completedBy)
        {
            try
            {
                string guildName = null;

                var guild = await GameDb.FindGUildById(guildId);
                if (guild == null)
                {
                    Logger.Instance.Error($"Lỗi thêm lịch sử quest: Không tìm thấy guild ID {guildId}");
                    return;
                }

                guildName = guild.g_name;

                // var result = DatabaseManager.Instance.GameDb.TblGroupQuestHistories.Add(new TblGroupQuestHistory
                // {
                //     QuestId = questId,
                //     GuildId = guildId,
                //     GuildName = guildName,
                //     FactionId = factionId,
                //     CompletedTime = DateTime.Now,
                //     CompletedBy = completedBy
                // });
                var result = await GameDb.AddQuestHistory(new tbl_group_quest_history() { questid = questId, guildid = guildId, guildname = guildName, factionid = factionId, completedtime = DateTime.Now, completedby = completedBy });
                if (result == null)
                {
                    throw new Exception("Không thể thêm mới lịch sử quest");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"Lỗi thêm lịch sử quest: {ex.Message}");
            }
        }


        #endregion
        #region Event Handlers

        /// <summary>
        /// Xử lý sự kiện khi người chơi tiêu diệt người chơi khác
        /// </summary>
        public void OnPlayerKillPlayer(PlayerPvp[] contributors)
        {
            try
            {
                // Xử lý các case nhân vật bị kill, hiện tại chỉ xử lý group quest
                ProcessGroupQuestKill(contributors);
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi xử lý sự kiện kill player: {ex.Message}");
            }
        }

        public async void ProcessGroupQuestKill(PlayerPvp[] contributors)
        {
            var target = contributors[0];
            var killer = contributors[1];
            var others = contributors.Skip(2).ToArray();

            var currentActiveQuests = GetGuildQuestProgress(killer.PlayerGuildId);

            if (target.PlayerFactionId == killer.PlayerFactionId  || target.PlayerGuildId == killer.PlayerGuildId )
            {
                return;
            }

            foreach (var quest in currentActiveQuests)
            {
                if (quest.QuestDefinition == null || quest.QuestDefinition.TargetType != TargetType.KillPlayers)
                {
                    continue;
                }
                //var process = AddGuildQuestContribution(killer.SessionId, killer.PlayerName, quest.QuestID, killer.PlayerGuildId, 1);
                var process = await AddGuildQuestContribution(killer.SessionId, killer.PlayerName, quest, killer.PlayerGuildId, 1);
                if (quest.IsCompleted)
                {
                    return;
                }
                await SendProgressToUser(process, quest);
            }

        }

        /// <summary>
        /// Xử lý quest khi kill boss
        /// </summary>
        private void ProcessBossKillQuests(int killerSessionID, string killerUserName, int killerGuildId, int killerFactionId, int bossID)
        {
            // Lấy danh sách quest tiêu diệt boss
            var bossQuests = _questDefinitions.Values.Where(q =>
                q.TargetType == TargetType.KillBoss &&
                (!q.TargetID.HasValue || q.TargetID.Value == bossID)).ToList();

            foreach (var quest in bossQuests)
            {
                // if (quest.QuestType == GroupQuestType.Guild && killerGuildId > 0)
                // {
                //     // Thêm đóng góp quest guild bằng cách truyền trực tiếp các giá trị
                //     AddGuildQuestContribution(killerSessionID, killerUserName, killerGuildId, quest.ID);
                // }
                // else if (quest.QuestType == GroupQuestType.Faction && killerFactionId > 0)
                // {
                //     // Thêm đóng góp quest faction bằng cách truyền trực tiếp các giá trị
                //     AddFactionQuestContribution(killerSessionID, killerUserName, killerFactionId, quest.ID);
                // }
            }
        }

        #endregion

        #region Save Methods



        /// <summary>
        /// Đánh dấu người chơi đã nhận thưởng
        /// </summary>
        /// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
        /// <param name="questId">ID của quest</param>
        /// <param name="groupId">ID của guild hoặc faction</param>
        /// <param name="playerId">ID của người chơi</param>
        public void MarkRewardReceived(int questType, int questId, int groupId, string playerName)
        {
            try
            {
                if (questType == 1) // Guild quest
                {
                    // Lấy đóng góp quest
                    var contribution = GetGuildQuestContribution(groupId, questId, playerName);
                    if (contribution != null)
                    {
                        contribution.HasReceivedReward = true;
                        SaveGuildQuestContribution(contribution, true);
                        Logger.Instance.Info( $"Đã đánh dấu nhận thưởng quest guild: Quest ID {questId}, Guild ID {groupId}, Player ID {playerName}");
                    }
                }
                else if (questType == 2) // Faction quest
                {
                    // Lấy đóng góp quest
                    var contribution = GetFactionQuestContribution(groupId, questId, playerName);
                    if (contribution != null)
                    {
                        contribution.HasReceivedReward = true;
                        SaveFactionQuestContribution(contribution, true);
                        Logger.Instance.Info( $"Đã đánh dấu nhận thưởng quest faction: Quest ID {questId}, Faction ID {groupId}, Player ID {playerName}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đánh dấu nhận thưởng quest: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy đóng góp quest guild của người chơi
        /// </summary>
        /// <param name="guildId">ID của guild</param>
        /// <param name="questId">ID của quest</param>
        /// <param name="playerId">ID của người chơi</param>
        /// <returns>Đối tượng đóng góp quest hoặc null nếu không tìm thấy</returns>
        public GroupQuestContribution GetGuildQuestContribution(int guildId, int questId, string playerName)
        {
            // Lấy tiến trình quest
            var progress = GetGuildQuestProgress(guildId, questId);

            if (progress == null)
            {
                return null;
            }

            // Tìm đóng góp của người chơi
            return progress.PlayerContributions.FirstOrDefault(c => c.PlayerName == playerName);
        }

        /// <summary>
        /// Lấy đóng góp quest faction của người chơi
        /// </summary>
        /// <param name="factionId">ID của faction</param>
        /// <param name="questId">ID của quest</param>
        /// <param name="playerId">ID của người chơi</param>
        /// <returns>Đối tượng đóng góp quest hoặc null nếu không tìm thấy</returns>
        public GroupQuestContribution GetFactionQuestContribution(int factionId, int questId, string playerName)
        {
            // Lấy tiến trình quest
            var progress = GetFactionQuestProgress(factionId, questId);

            if (progress == null)
            {
                return null;
            }

            // Tìm đóng góp của người chơi
            return progress.PlayerContributions.FirstOrDefault(c => c.PlayerName == playerName);
        }


        /// <summary>
        /// Lưu tiến trình quest guild vào database
        /// </summary>
        public async void SaveGuildQuestProgress(GroupQuestProgress progress, bool immediate = false)
        {
            if (progress == null)
                return;

            if (immediate)
            {
                // Lưu ngay lập tức
                try
                {
                    int guildId = (int)progress.GuildID;

                    if (progress.ID <= 0)
                    {
                        if (progress.IsCompleted)
                        {
                            NotifyQuestComplete(1, progress, guildId);
                        }

                        // Cập nhật LastCompletedTime nếu quest đã hoàn thành
                        if (progress.Status == QuestStatus.Completed && progress.CompletedTime.HasValue)
                        {
                            progress.LastCompletedTime = progress.CompletedTime;
                        }
                        // var result = DatabaseManager.Instance.GameDb.TblGuildQuestProgresses.Add(new TblGuildQuestProgress
                        // {
                        //     GuildId = guildId,
                        //     QuestId = progress.QuestID,
                        //     CurrentCount = progress.CurrentCount,
                        //     Status = (byte)progress.Status,
                        //     AcceptedTime = progress.AcceptedTime,
                        //     LastUpdateTime = progress.LastUpdateTime,
                        //     LastResetTime = progress.LastResetTime,
                        //     CompletedTime = progress.CompletedTime,
                        //     CancelledTime = progress.CancelledTime,
                        //     LastCompletedTime = progress.LastCompletedTime
                        // });
                        // DatabaseManager.Instance.GameDb.SaveChanges();
                        var res = await GameDb.CreateGuildQuestProgress(new tbl_guild_quest_progress() { guildid = guildId, questid = progress.QuestID, currentcount = progress.CurrentCount, status = (byte)progress.Status, acceptedtime = progress.AcceptedTime, lastupdatetime = progress.LastUpdateTime, lastresettime = progress.LastResetTime, completedtime = progress.CompletedTime, cancelledtime = progress.CancelledTime, lastcompletedtime = progress.LastCompletedTime });
                        progress.ID = (int) res.id;

                    }
                    else
                    {
                        // Đã có ID, cập nhật
                        // Cập nhật LastCompletedTime nếu quest đã hoàn thành
                        if (progress.Status == QuestStatus.Completed && progress.CompletedTime.HasValue)
                        {
                            progress.LastCompletedTime = progress.CompletedTime;
                        }

                        // string query = @"
                        //     UPDATE TBL_GUILD_QUEST_PROGRESS SET
                        //     CurrentCount = @CurrentCount,
                        //     Status = @Status,
                        //     LastUpdateTime = @LastUpdateTime,
                        //     CompletedTime = @CompletedTime,
                        //     CancelledTime = @CancelledTime,
                        //     LastCompletedTime = @LastCompletedTime
                        //     WHERE ID = @ID";
                        // Nếu quest đã hoàn thành, gửi thông báo hoàn thành
                        if (progress.IsCompleted)
                        {
                            await NotifyQuestComplete(1, progress, guildId);
                        }

                        // Cập nhật LastCompletedTime nếu quest đã hoàn thành
                        if (progress.Status == QuestStatus.Completed && progress.CompletedTime.HasValue)
                        {
                            progress.LastCompletedTime = progress.CompletedTime;
                        }
                        // SqlParameter[] parameters = new SqlParameter[]
                        // {
                        //     new("@ID", progress.ID),
                        //     new("@CurrentCount", progress.CurrentCount),
                        //     new("@Status", (int)progress.Status),
                        //     new("@LastUpdateTime", progress.LastUpdateTime),
                        //     new("@CompletedTime", progress.CompletedTime.HasValue ? progress.CompletedTime.Value : DBNull.Value),
                        //     new("@CancelledTime", progress.CancelledTime.HasValue ? progress.CancelledTime.Value : DBNull.Value),
                        //     new("@LastCompletedTime", progress.LastCompletedTime.HasValue ? progress.LastCompletedTime.Value : DBNull.Value)
                        // };

                        // int result = DBA.ExeSqlCommand2(query, parameters, "heroGame");
                        // var result = DatabaseManager.Instance.GameDb.TblGuildQuestProgresses.Where(x => x.Id == progress.ID).FirstOrDefault();
                        // if (result != null)
                        // {
                        //     result.CurrentCount = progress.CurrentCount;
                        //     result.Status = (byte)progress.Status;
                        //     result.LastUpdateTime = progress.LastUpdateTime;
                        //     result.CompletedTime = progress.CompletedTime;
                        //     result.CancelledTime = progress.CancelledTime;
                        //     result.LastCompletedTime = progress.LastCompletedTime;
                        //     DatabaseManager.Instance.GameDb.SaveChanges();
                        // }
                        var result = await GameDb.UpdateGuildQuestProgress(new tbl_guild_quest_progress() { id = progress.ID, currentcount = progress.CurrentCount, status = (short)progress.Status, lastupdatetime = progress.LastUpdateTime, completedtime = progress.CompletedTime, cancelledtime = progress.CancelledTime, lastcompletedtime = progress.LastCompletedTime });

                        if (result != null)
                        {
                            //Form1.WriteLine(3, $"Đã cập nhật tiến trình quest guild: Guild ID {progress.GuildID}, Quest ID {progress.QuestID}, ID {progress.ID}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Info( $"Lỗi lưu tiến trình quest guild: {ex.Message}");
                }
            }
            else
            {
                // Thêm vào danh sách chờ lưu
                lock (_pendingGuildQuestLock)
                {
                    _pendingGuildQuestSaves.Add(progress);
                }
            }
        }

        /// <summary>
        /// Lưu đóng góp quest guild vào database
        /// </summary>
        public async void SaveGuildQuestContribution(GroupQuestContribution contribution, bool immediate = false)
        {
            if (contribution == null)
                return;

            if (immediate)
            {
                // Lưu ngay lập tức
                try
                {
                    if (contribution.ID <= 0)
                    {

                        var result = await GameDb.CreateGuildQuestContribution(new tbl_group_quest_contribution() { questid = contribution.QuestID, progressid = contribution.ProgressID, playerid = contribution.PlayerID, playername = contribution.PlayerName, guildid = contribution.GuildID, actiontype = contribution.ActionType, targetid = contribution.TargetID, targetname = contribution.TargetName, contributioncount = contribution.ContributionCount, contributiontime = contribution.ContributionTime, hasreceivedreward = contribution.HasReceivedReward });
                        contribution.ID = (int)result.id;

                        if (result != null)
                        {
                            Logger.Instance.Info("Error SaveGuildQuestContribution");
                        }
                    }
                    else
                    {
                        var result = await GameDb.UpdateGuildQuestContribution(new tbl_group_quest_contribution() { id = contribution.ID, contributioncount = contribution.ContributionCount, contributiontime = contribution.ContributionTime, hasreceivedreward = contribution.HasReceivedReward, actiontype = contribution.ActionType, targetid = contribution.TargetID, targetname = contribution.TargetName });
                        if (result == null)
                        {
                            Logger.Instance.Info("Error SaveGuildQuestContribution");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Info( $"Lỗi lưu đóng góp quest guild: {ex.Message}");
                }
            }
            else
            {
                // Thêm vào danh sách chờ lưu
                lock (_pendingGuildContributionLock)
                {
                    _pendingGuildContributionSaves.Add(contribution);
                }
            }
        }


        /// <summary>
        /// Gửi thông báo hoàn thành quest đến tất cả các GS
        /// </summary>
        /// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
        /// <param name="questId">ID của quest</param>
        /// <param name="groupId">ID của guild hoặc faction</param>
        private async Task NotifyQuestComplete(int questType, GroupQuestProgress progress, int groupId)
        {
            try
            {
                // Gửi thông báo đến tất cả các GS
                // client.Sendd($"GROUP_QUEST_COMPLETE|{guildId}|{guildMessage}|{questId}");
                var quest = progress.QuestDefinition; // GetQuestDefinition(questId); // Lấy tên quest từ questId sau khi hoàn thành quest
                string guildMessage = $"Đã hoàn thành nhiệm vụ [{quest.QuestName}]! Hãy đến nhận thưởng!"; // Lấy tên quest từ questId sau khi hoàn thành quest // Lấy tên quest từ questId sau khi hoàn thành quest
                string message = $"GROUP_QUEST_COMPLETE|{groupId}|{guildMessage}|{progress.QuestID}|{progress.ID}|{progress.CurrentCount}";
                //World.SendToAllServer(message);
                await ClusterManager.Instance.BroadCastPacketMessage(message);
                Logger.Instance.Info( $"Đã gửi thông báo hoàn thành quest: {message}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi gửi thông báo hoàn thành quest: {ex.Message}");
            }
        }

        /// <summary>
        /// Lưu tiến trình quest faction vào database
        /// </summary>
        public void SaveFactionQuestProgress(GroupQuestProgress progress, bool immediate = false)
        {
            if (progress == null)
                return;

            if (immediate)
            {
                // Lưu ngay lập tức
                try
                {
                    // int factionId = (int)progress.FactionID;

                    // if (progress.ID <= 0)
                    // {
                      
                    //     var result = DatabaseManager.Instance.GameDb.TblFactionQuestProgresses.Add(new TblFactionQuestProgress
                    //     {
                    //         FactionId = factionId,
                    //         QuestId = progress.QuestID,
                    //         CurrentCount = progress.CurrentCount,
                    //         Status = (byte)progress.Status,
                    //         AcceptedTime = progress.AcceptedTime,
                    //         LastUpdateTime = progress.LastUpdateTime,
                    //         LastResetTime = progress.LastResetTime,
                    //         CompletedTime = progress.CompletedTime,
                    //         CancelledTime = progress.CancelledTime
                    //     });
                    //     DatabaseManager.Instance.GameDb.SaveChanges();
                    //     progress.ID = result.Entity.Id;

                    // }
                    // else
                    // {
                      
                    //     // int result = DBA.ExeSqlCommand2(query, parameters, "heroGame");
                    //     var result = DatabaseManager.Instance.GameDb.TblFactionQuestProgresses.Where(x => x.Id == progress.ID).FirstOrDefault();
                    //     if (result != null)
                    //     {
                    //         result.CurrentCount = progress.CurrentCount;
                    //         result.Status = (byte)progress.Status;
                    //         result.LastUpdateTime = progress.LastUpdateTime;
                    //         result.CompletedTime = progress.CompletedTime;
                    //         result.CancelledTime = progress.CancelledTime;
                    //         DatabaseManager.Instance.GameDb.SaveChanges();
                    //     }

                    // }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Info( $"Lỗi lưu tiến trình quest faction: {ex.Message}");
                }
            }
            else
            {
                // Thêm vào danh sách chờ lưu
                lock (_pendingFactionQuestLock)
                {
                    _pendingFactionQuestSaves.Add(progress);
                }
            }
        }

        /// <summary>
        /// Lưu đóng góp quest faction vào database
        /// </summary>
        public void SaveFactionQuestContribution(GroupQuestContribution contribution, bool immediate = false)
        {
            if (contribution == null)
                return;

            if (immediate)
            {
                // Lưu ngay lập tức
                try
                {
                    if (contribution.ID <= 0)
                    {
                        // Chưa có ID, thêm mới
                        //     string query = @"
                        //         INSERT INTO TBL_GROUP_QUEST_CONTRIBUTION
                        //         (QuestID, PlayerID, PlayerName, FactionID, ContributionCount, ContributionTime, HasReceivedReward)
                        //         VALUES
                        //         (@QuestID, @PlayerID, @PlayerName, @FactionID, @ContributionCount, @ContributionTime, @HasReceivedReward);
                        //         SELECT SCOPE_IDENTITY();";

                        //     SqlParameter[] parameters = new SqlParameter[]
                        //     {
                        //         new("@QuestID", contribution.QuestID),
                        //         new("@PlayerID", contribution.PlayerID),
                        //         new("@PlayerName", contribution.PlayerName),
                        //         new("@FactionID", contribution.FactionID),
                        //         new("@ContributionCount", contribution.ContributionCount),
                        //         new("@ContributionTime", contribution.ContributionTime),
                        //         new("@HasReceivedReward", contribution.HasReceivedReward)
                        //     };

                        //    DBA.ExeSqlCommand2(query, parameters, "heroGame");
                        // var result = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Add(new TblGroupQuestContribution
                        // {
                        //     QuestId = contribution.QuestID,
                        //     PlayerId = contribution.PlayerID,
                        //     PlayerName = contribution.PlayerName,
                        //     FactionId = contribution.FactionID,
                        //     ContributionCount = contribution.ContributionCount,
                        //     ContributionTime = contribution.ContributionTime,
                        //     HasReceivedReward = contribution.HasReceivedReward
                        // });
                        // DatabaseManager.Instance.GameDb.SaveChanges();
                        // contribution.ID = result.Entity.Id;

                    }
                    else
                    {
                        // Đã có ID, cập nhật
                        // string query = @"
                        //     UPDATE TBL_GROUP_QUEST_CONTRIBUTION SET
                        //     ContributionCount = @ContributionCount,
                        //     ContributionTime = @ContributionTime,
                        //     HasReceivedReward = @HasReceivedReward
                        //     WHERE ID = @ID";

                        // SqlParameter[] parameters = new SqlParameter[]
                        // {
                        //     new("@ID", contribution.ID),
                        //     new("@ContributionCount", contribution.ContributionCount),
                        //     new("@ContributionTime", contribution.ContributionTime),
                        //     new("@HasReceivedReward", contribution.HasReceivedReward)
                        // };

                        // int result = DBA.ExeSqlCommand2(query, parameters, "heroGame");

                        // if (result > 0)
                        // {
                        //     //Form1.WriteLine(3, $"Đã cập nhật đóng góp quest faction: Player ID {contribution.PlayerID}, Quest ID {contribution.QuestID}, ID {contribution.ID}");
                        // }
                        // var result = DatabaseManager.Instance.GameDb.TblGroupQuestContributions.Where(x => x.Id == contribution.ID).FirstOrDefault();
                        // if (result != null)
                        // {
                        //     result.ContributionCount = contribution.ContributionCount;
                        //     result.ContributionTime = contribution.ContributionTime;
                        //     result.HasReceivedReward = contribution.HasReceivedReward;
                        //     DatabaseManager.Instance.GameDb.SaveChanges();
                        // }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Info( $"Lỗi lưu đóng góp quest faction: {ex.Message}");
                }
            }
            else
            {
                // Thêm vào danh sách chờ lưu
                lock (_pendingFactionContributionLock)
                {
                    _pendingFactionContributionSaves.Add(contribution);
                }
            }
        }

        /// <summary>
        /// Lưu tất cả dữ liệu đang chờ
        /// </summary>
        private void SavePendingData(object sender, ElapsedEventArgs e)
        {
            try
            {
                int guildQuestCount = 0;
                int factionQuestCount = 0;
                int guildContributionCount = 0;
                int factionContributionCount = 0;

                // Lưu tiến trình quest guild
                HashSet<GroupQuestProgress> guildQuestToSave;
                lock (_pendingGuildQuestLock)
                {
                    guildQuestToSave = new HashSet<GroupQuestProgress>(_pendingGuildQuestSaves);
                    _pendingGuildQuestSaves.Clear();
                }

                foreach (var progress in guildQuestToSave)
                {
                    SaveGuildQuestProgress(progress, true);
                    guildQuestCount++;
                }

                // Lưu tiến trình quest faction
                HashSet<GroupQuestProgress> factionQuestToSave;
                lock (_pendingFactionQuestLock)
                {
                    factionQuestToSave = new HashSet<GroupQuestProgress>(_pendingFactionQuestSaves);
                    _pendingFactionQuestSaves.Clear();
                }

                foreach (var progress in factionQuestToSave)
                {
                    SaveFactionQuestProgress(progress, true);
                    factionQuestCount++;
                }

                // Lưu đóng góp quest guild
                HashSet<GroupQuestContribution> guildContributionToSave;
                lock (_pendingGuildContributionLock)
                {
                    guildContributionToSave = new HashSet<GroupQuestContribution>(_pendingGuildContributionSaves);
                    _pendingGuildContributionSaves.Clear();
                }

                foreach (var contribution in guildContributionToSave)
                {
                    SaveGuildQuestContribution(contribution, true);
                    guildContributionCount++;
                }

                // Lưu đóng góp quest faction
                HashSet<GroupQuestContribution> factionContributionToSave;
                lock (_pendingFactionContributionLock)
                {
                    factionContributionToSave = new HashSet<GroupQuestContribution>(_pendingFactionContributionSaves);
                    _pendingFactionContributionSaves.Clear();
                }

                foreach (var contribution in factionContributionToSave)
                {
                    SaveFactionQuestContribution(contribution, true);
                    factionContributionCount++;
                }

                // Ghi log nếu có dữ liệu được lưu
                int totalSaved = guildQuestCount + factionQuestCount + guildContributionCount + factionContributionCount;
                if (totalSaved > 0)
                {
                    //Logger.Instance.Info( $"Đã lưu {totalSaved} dữ liệu quest: {guildQuestCount} guild quest, {factionQuestCount} faction quest, {guildContributionCount} guild contribution, {factionContributionCount} faction contribution");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi lưu dữ liệu quest: {ex.Message}");
            }
        }

        #endregion

        #region Synchronization Methods

        /// <summary>
        /// Đồng bộ tiến trình quest guild giữa các LS
        /// </summary>
        public void SyncGuildQuestProgress(int questId, int guildId, int currentCount, QuestStatus status)
        {
            try
            {
                // Kiểm tra xem quest có tồn tại không
                if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Guild)
                {
                    Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Guild");
                    return;
                }

                // Lấy tiến trình quest hiện tại
                var progress = GetGuildQuestProgress(guildId, questId);

                if (progress == null)
                {
                    // Nếu chưa có tiến trình, tạo mới
                    progress = new GroupQuestProgress
                    {
                        QuestID = questId,
                        GuildID = guildId,
                        CurrentCount = 0,
                        Status = QuestStatus.Accepted,
                        AcceptedTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now,
                        LastResetTime = DateTime.Now,
                        QuestDefinition = questDef
                    };

                    // Thêm vào danh sách
                    if (!_guildQuestProgress.ContainsKey(guildId))
                    {
                        _guildQuestProgress[guildId] = new List<GroupQuestProgress>();
                    }
                    _guildQuestProgress[guildId].Add(progress);

                    // Lưu vào database
                    SaveGuildQuestProgress(progress);
                }

                // Cập nhật tiến trình
                progress.CurrentCount = currentCount;
                progress.Status = status;
                progress.LastUpdateTime = DateTime.Now;

                if (status == QuestStatus.Completed && !progress.CompletedTime.HasValue)
                {
                    progress.CompletedTime = DateTime.Now;
                }

                // Cập nhật vào database
                UpdateGuildQuestProgress(progress);

                Logger.Instance.Info( $"Đã đồng bộ tiến trình quest guild: Quest ID {questId}, Guild ID {guildId}, Count {currentCount}, Status {status}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đồng bộ tiến trình quest guild: {ex.Message}");
            }
        }

        /// <summary>
        /// Đồng bộ tiến trình quest faction giữa các LS
        /// </summary>
        public void SyncFactionQuestProgress(int questId, int factionId, int currentCount, QuestStatus status)
        {
            try
            {
                // Kiểm tra xem quest có tồn tại không
                if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Faction)
                {
                    Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Faction");
                    return;
                }

                // Lấy tiến trình quest hiện tại
                var progress = GetFactionQuestProgress(factionId, questId);

                if (progress == null)
                {
                    // Nếu chưa có tiến trình, tạo mới
                    progress = new GroupQuestProgress
                    {
                        QuestID = questId,
                        FactionID = factionId,
                        CurrentCount = 0,
                        Status = QuestStatus.Accepted,
                        AcceptedTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now,
                        LastResetTime = DateTime.Now,
                        QuestDefinition = questDef
                    };

                    // Thêm vào danh sách
                    if (!_factionQuestProgress.ContainsKey(factionId))
                    {
                        _factionQuestProgress[factionId] = new List<GroupQuestProgress>();
                    }
                    _factionQuestProgress[factionId].Add(progress);

                    // Lưu vào database (ngay lập tức khi tạo mới)
                    SaveFactionQuestProgress(progress, true);
                }

                // Cập nhật tiến trình
                progress.CurrentCount = currentCount;
                progress.Status = status;
                progress.LastUpdateTime = DateTime.Now;

                if (status == QuestStatus.Completed && !progress.CompletedTime.HasValue)
                {
                    progress.CompletedTime = DateTime.Now;
                }

                // Cập nhật vào database
                SaveFactionQuestProgress(progress);

                //Logger.Instance.Info( $"Đã đồng bộ tiến trình quest faction: Quest ID {questId}, Faction ID {factionId}, Count {currentCount}, Status {status}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đồng bộ tiến trình quest faction: {ex.Message}");
            }
        }

        /// <summary>
        /// Đồng bộ đóng góp quest guild giữa các LS
        /// </summary>
        public void SyncGuildQuestContribution(int targetId, int targetLevel, int guildId, int playerId, string playerName, int playerLevel, int contributionCount, string serverId)
        {
            try
            {
                var currentActiveQuests = GetGuildQuestProgress(guildId);
                var qType = targetId > 10000 ? TargetType.KillMonsters : TargetType.KillPlayers;
                // get quest defination


                switch (qType)
                {
                    case TargetType.KillMonsters:
                        // Chỉ xử lý khi chênh lệch cấp độ lớn hơn hoặc bằng 10
                         foreach (var quest in currentActiveQuests)
                            {
                                if (quest.QuestDefinition != null && quest.QuestDefinition.TargetType == TargetType.KillMonsters && quest.Status != QuestStatus.Completed && quest.Status != QuestStatus.Cancelled) //&& quest.Status != QuestStatus.Completed)
                                {
                                    //Logger.Instance.Info( $"Thêm đóng góp quest monster: QuestID={quest.QuestID}, GuildID={guildId}, PlayerID={playerId}");
                                    if (Math.Abs(playerLevel - targetLevel) <= 10)
                                    {
                                        var process = AddGuildQuestContribution(playerId, playerName, quest, guildId, contributionCount, 1, targetId, null);
                                    }
                                    
                                }
                                if (quest.QuestDefinition != null && quest.QuestDefinition.TargetType == TargetType.KillBoss &&  quest.Status != QuestStatus.Completed && quest.Status != QuestStatus.Cancelled)
                                {
                                    Logger.Instance.Info( "Handle Boss Quest ");
                                }

                            }
                       
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đồng bộ đóng góp quest guild: {ex.Message}");
            }
        }

        public async Task SendProgressToUser( GroupQuestContribution contribution, GroupQuestProgress quest)
        {
            try
            {
                _questDefinitions.TryGetValue(contribution.QuestID, out var questDef);
                if (questDef == null)
                    return;

                await ClusterManager.Instance.BroadCastPacketMessage($"GROUP_QUEST_PROGRESS|{contribution.PlayerID}|{contribution.PlayerName}|{questDef.QuestName}|{quest.CurrentCount}|{contribution.ContributionCount}|{questDef.TargetCount}");
               
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi gửi thông tin tiến trình quest đến GS: {ex.Message}");
            }
        }

        /// <summary>
        /// Đồng bộ đóng góp quest faction giữa các LS
        /// </summary>
        public void SyncFactionQuestContribution(int targetId, int targetLevel, int factionId, int playerId, string playerName, int playerLevel, int contributionCount)
        {
            try
            {
                // TODO:
                // Nhiệm vụ chính tà
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đồng bộ đóng góp quest faction: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi thông tin cập nhật quest đến tất cả GS
        /// </summary>
        public async Task SendQuestUpdateToAllGS(int questType, int questId, int groupId, int currentCount, QuestStatus status)
        {
            try
            {
                // Gửi thông tin cập nhật đến tất cả GS
                await ClusterManager.Instance.BroadCastPacketMessage($"GROUP_QUEST_UPDATE|{questType}|{questId}|{groupId}|{currentCount}|{(int)status}");

                Logger.Instance.Info( $"Đã gửi cập nhật quest đến tất cả GS: Type {questType}, Quest ID {questId}, Group ID {groupId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi gửi cập nhật quest đến GS: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi thông tin đóng góp quest đến tất cả GS
        /// </summary>
        public async Task SendContributionUpdateToAllGS(int questType, int questId, int groupId, int playerId, string playerName, int contributionCount)
        {
            try
            {
                // Gửi thông tin đóng góp đến tất cả GS
                await ClusterManager.Instance.BroadCastPacketMessage($"GROUP_QUEST_CONTRIBUTION|{questType}|{questId}|{groupId}|{playerId}|{playerName}|{contributionCount}");
                
                Logger.Instance.Info( $"Đã gửi đóng góp quest đến tất cả GS: Type {questType}, Quest ID {questId}, Group ID {groupId}, Player ID {playerId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi gửi đóng góp quest đến GS: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi thông báo hoàn thành quest đến tất cả GS
        /// </summary>
        public async Task SendQuestCompleteToAllGS(int questType, int questId, int groupId)
        {
            try
            {
                // Gửi thông báo hoàn thành đến tất cả GS
                await ClusterManager.Instance.BroadCastPacketMessage($"GROUP_QUEST_COMPLETE|{questType}|{questId}|{groupId}");
                Logger.Instance.Info( $"Đã gửi cập nhật hoàn thành quest đến tất cả GS: Type {questType}, Quest ID {questId}, Group ID {groupId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi gửi hoàn thành quest đến GS: {ex.Message}");
            }
        }

        /// <summary>
        /// Hoàn thành quest guild
        /// </summary>
        public async Task<bool> CompleteGuildQuest(int questId, int guildId)
        {
            try
            {
                // Kiểm tra xem quest có tồn tại không
                if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Guild)
                {
                    Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Guild");
                    return false;
                }

                // Lấy tiến trình quest hiện tại
                var progress = GetGuildQuestProgress(guildId, questId);

                if (progress == null)
                {
                    // Nếu chưa có tiến trình, tạo mới
                    progress = new GroupQuestProgress
                    {
                        QuestID = questId,
                        GuildID = guildId,
                        CurrentCount = questDef.TargetCount, // Đặt là đã hoàn thành
                        Status = QuestStatus.Completed,
                        AcceptedTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now,
                        LastResetTime = DateTime.Now,
                        CompletedTime = DateTime.Now,
                        QuestDefinition = questDef
                    };

                    // Thêm vào danh sách
                    if (!_guildQuestProgress.ContainsKey(guildId))
                    {
                        _guildQuestProgress[guildId] = new List<GroupQuestProgress>();
                    }
                    _guildQuestProgress[guildId].Add(progress);

                    // Lưu vào database (ngay lập tức khi tạo mới)
                    SaveGuildQuestProgress(progress, true);
                }
                else if (progress.Status != QuestStatus.Completed)
                {
                    // Cập nhật tiến trình
                    progress.CurrentCount = questDef.TargetCount;
                    progress.Status = QuestStatus.Completed;
                    progress.LastUpdateTime = DateTime.Now;
                    progress.CompletedTime = DateTime.Now;

                    // Cập nhật vào database (ngay lập tức khi hoàn thành)
                    SaveGuildQuestProgress(progress, true);
                }
                else
                {
                    // Đã hoàn thành rồi, không cần làm gì
                    return true;
                }

                // Gửi thông báo đến tất cả GS
                SendQuestCompleteToAllGS(1, questId, guildId);

                // Lấy tên guild
                // string guildName = "";
                // foreach (playerS player in World.Players.Values)
                // {
                //     if (player.GuildId == guildId && !string.IsNullOrEmpty(player.GuildName))
                //     {
                //         guildName = player.GuildName;
                //         break;
                //     }
                // }

                // Gửi thông báo đến tất cả người chơi trong guild
                await ClusterManager.Instance.BroadCastPacketMessage($"GroupQuestMessage|1|{guildId}|Nhiệm vụ bang hội [{questDef.QuestName}] đã hoàn thành!|10|0");
                Logger.Instance.Info( $"Đã hoàn thành quest guild: Quest ID {questId}, Guild ID {guildId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi hoàn thành quest guild: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Hoàn thành quest faction
        /// </summary>
        public async Task<bool> CompleteFactionQuest(int questId, int factionId)
        {
            try
            {
                // Kiểm tra xem quest có tồn tại không
                if (!_questDefinitions.TryGetValue(questId, out var questDef) || questDef.QuestType != GroupQuestType.Faction)
                {
                    Logger.Instance.Info( $"Không tìm thấy quest ID {questId} hoặc quest không phải loại Faction");
                    return false;
                }

                // Lấy tiến trình quest hiện tại
                var progress = GetFactionQuestProgress(factionId, questId);

                if (progress == null)
                {
                    // Nếu chưa có tiến trình, tạo mới
                    progress = new GroupQuestProgress
                    {
                        QuestID = questId,
                        FactionID = factionId,
                        CurrentCount = questDef.TargetCount, // Đặt là đã hoàn thành
                        Status = QuestStatus.Completed,
                        AcceptedTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now,
                        LastResetTime = DateTime.Now,
                        CompletedTime = DateTime.Now,
                        QuestDefinition = questDef
                    };

                    // Thêm vào danh sách
                    if (!_factionQuestProgress.ContainsKey(factionId))
                    {
                        _factionQuestProgress[factionId] = new List<GroupQuestProgress>();
                    }
                    _factionQuestProgress[factionId].Add(progress);

                    // Lưu vào database (ngay lập tức khi tạo mới)
                    SaveFactionQuestProgress(progress, true);
                }
                else if (progress.Status != QuestStatus.Completed)
                {
                    // Cập nhật tiến trình
                    progress.CurrentCount = questDef.TargetCount;
                    progress.Status = QuestStatus.Completed;
                    progress.LastUpdateTime = DateTime.Now;
                    progress.CompletedTime = DateTime.Now;

                    // Cập nhật vào database (ngay lập tức khi hoàn thành)
                    SaveFactionQuestProgress(progress, true);
                }
                else
                {
                    // Đã hoàn thành rồi, không cần làm gì
                    return true;
                }

                // Gửi thông báo đến tất cả GS
                SendQuestCompleteToAllGS(2, questId, factionId);

                // Gửi thông báo đến tất cả người chơi trong faction
                string factionName = factionId == 1 ? "Chính Phái" : "Tà Phái";
                await ClusterManager.Instance.BroadCastPacketMessage($"GroupQuestMessage|2|{factionId}|Nhiệm vụ thế lực [{questDef.QuestName}] đã hoàn thành!|10|0");
         

                Logger.Instance.Info( $"Đã hoàn thành quest faction: Quest ID {questId}, Faction ID {factionId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi hoàn thành quest faction: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
