using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using Grpc.Core;
using HeroLogin.Core;
using HeroLogin.Protos;
using Microsoft.AspNetCore.Authorization;

namespace HeroLogin.Services
{
    public class WebAdminService : WebAdmin.WebAdminBase
    {
        private readonly ILogger<WebAdminService> _logger;
        private readonly ConfigManager _configManager;
        private static readonly Dictionary<string, string> _activeSessions = new();
        private static readonly Dictionary<string, DateTime> _sessionExpiry = new();
        private static readonly Dictionary<int, Process> _gameServerProcesses = new();

        // Simple in-memory user store for demo (should use database in production)
        private static readonly Dictionary<string, AdminUser> _users = new()
        {
            ["admin"] = new AdminUser
            {
                Id = 1,
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                Email = "<EMAIL>",
                FirstName = "Super",
                LastName = "Admin",
                Roles = new[] { "SuperAdmin" },
                Permissions = new[] { "servers:*", "players:*", "configs:*", "users:*" },
                IsActive = true
            },
            ["operator"] = new AdminUser
            {
                Id = 2,
                Username = "operator",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("operator123"),
                Email = "<EMAIL>",
                FirstName = "Game",
                LastName = "Operator",
                Roles = new[] { "Operator" },
                Permissions = new[] { "servers:read", "players:read", "players:kick" },
                IsActive = true
            }
        };

        public WebAdminService(ILogger<WebAdminService> logger)
        {
            _logger = logger;
            _configManager = ConfigManager.Instance;
        }

        #region Authentication

        public override async Task<LoginResponse> Login(LoginRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation($"WebAdmin login attempt for user: {request.Username} from IP: {request.IpAddress}");

                // Validate user credentials
                if (!_users.TryGetValue(request.Username, out var user) || !user.IsActive)
                {
                    return new LoginResponse
                    {
                        Success = false,
                        Message = "Invalid credentials"
                    };
                }

                if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                {
                    return new LoginResponse
                    {
                        Success = false,
                        Message = "Invalid credentials"
                    };
                }

                // Generate tokens
                var accessToken = GenerateAccessToken(user);
                var refreshToken = GenerateRefreshToken(user);

                // Store session
                _activeSessions[accessToken] = user.Username;
                _sessionExpiry[accessToken] = DateTime.UtcNow.AddHours(1);

                // Update last login
                user.LastLoginAt = DateTime.UtcNow;

                _logger.LogInformation($"WebAdmin login successful for user: {request.Username}");

                return new LoginResponse
                {
                    Success = true,
                    Message = "Login successful",
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresIn = 3600,
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Username = user.Username,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Roles = { user.Roles },
                        Permissions = { user.Permissions },
                        IsActive = user.IsActive,
                        LastLoginAt = user.LastLoginAt.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during WebAdmin login");
                return new LoginResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        public override async Task<RefreshTokenResponse> RefreshToken(RefreshTokenRequest request, ServerCallContext context)
        {
            try
            {
                // Simple refresh token validation (in production, use proper JWT validation)
                if (!ValidateRefreshToken(request.RefreshToken, out var username))
                {
                    return new RefreshTokenResponse
                    {
                        Success = false,
                        Message = "Invalid refresh token"
                    };
                }

                var user = _users[username];
                var newAccessToken = GenerateAccessToken(user);
                var newRefreshToken = GenerateRefreshToken(user);

                // Update session
                _activeSessions[newAccessToken] = username;
                _sessionExpiry[newAccessToken] = DateTime.UtcNow.AddHours(1);

                return new RefreshTokenResponse
                {
                    Success = true,
                    Message = "Token refreshed successfully",
                    AccessToken = newAccessToken,
                    RefreshToken = newRefreshToken,
                    ExpiresIn = 3600
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return new RefreshTokenResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        public override async Task<LogoutResponse> Logout(LogoutRequest request, ServerCallContext context)
        {
            try
            {
                // Remove session
                _activeSessions.Remove(request.AccessToken);
                _sessionExpiry.Remove(request.AccessToken);

                return new LogoutResponse
                {
                    Success = true,
                    Message = "Logged out successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return new LogoutResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        #endregion

        #region Server Management

        public override async Task<GetServerListResponse> GetServerList(GetServerListRequest request, ServerCallContext context)
        {
            try
            {
                if (!await ValidateTokenAsync(context))
                {
                    throw new RpcException(new Status(StatusCode.Unauthenticated, "Invalid or expired token"));
                }

                var servers = new List<ServerInfo>();
                var clusterManager = ClusterManager.Instance;
                var clusters = clusterManager.GetAllClusters();

                foreach (var cluster in clusters)
                {
                    // Iterate through each server channel in the cluster
                    foreach (var channel in cluster.Channels)
                    {
                        var serverInfo = new ServerInfo
                        {
                            ServerId = channel.ServerID,
                            ServerName = channel.ServerName,
                            ServerIp = channel.ServerIP,
                            GameServerPort = channel.GameServerPort,
                            GrpcPort = channel.GameServerGrpcPort,
                            Status = channel.Status ? ServerStatus.Online : ServerStatus.Offline,
                            OnlinePlayers = channel.CurrentPlayers,
                            MaxPlayers = channel.MaximumOnline,
                            ClusterId = cluster.ID,
                            Uptime = channel.Status ? GetServerUptime(channel.ServerID) : "N/A",
                            Version = "1.0.0"
                        };
                        servers.Add(serverInfo);
                    }
                }

                return new GetServerListResponse
                {
                    Success = true,
                    Message = "Server list retrieved successfully",
                    Servers = { servers }
                };
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server list");
                return new GetServerListResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        public override async Task<GetServerStatusResponse> GetServerStatus(GetServerStatusRequest request, ServerCallContext context)
        {
            try
            {
                if (!await ValidateTokenAsync(context))
                {
                    throw new RpcException(new Status(StatusCode.Unauthenticated, "Invalid or expired token"));
                }

                var clusterManager = ClusterManager.Instance;
                var server = clusterManager.GetServerChannel(1, request.ServerId); // Assuming cluster ID 1

                if (server == null)
                {
                    return new GetServerStatusResponse
                    {
                        Success = false,
                        Message = "Server not found"
                    };
                }

                var serverInfo = new ServerInfo
                {
                    ServerId = server.ServerID,
                    ServerName = server.ServerName,
                    ServerIp = server.ServerIP,
                    GameServerPort = server.GameServerPort,
                    GrpcPort = server.GameServerGrpcPort,
                    Status = server.Status ? ServerStatus.Online : ServerStatus.Offline,
                    OnlinePlayers = server.CurrentPlayers,
                    MaxPlayers = server.MaximumOnline,
                    ClusterId = 1,
                    Uptime = server.Status ? GetServerUptime(server.ServerID) : "N/A",
                    Version = "1.0.0"
                };

                var metrics = new ServerMetrics
                {
                    CpuUsage = GetCpuUsage(),
                    MemoryUsage = GetMemoryUsage(),
                    MemoryTotal = GetTotalMemory(),
                    MemoryUsed = GetUsedMemory(),
                    ActiveConnections = server.CurrentPlayers,
                    TotalConnections = server.CurrentPlayers,
                    NetworkIn = 0, // TODO: Implement network metrics
                    NetworkOut = 0,
                    Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                };

                return new GetServerStatusResponse
                {
                    Success = true,
                    Message = "Server status retrieved successfully",
                    Server = serverInfo,
                    Metrics = metrics
                };
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting server status for server {ServerId}", request.ServerId);
                return new GetServerStatusResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        public override async Task<StartGameServerResponse> StartGameServer(StartGameServerRequest request, ServerCallContext context)
        {
            try
            {
                if (!await ValidateTokenAsync(context))
                {
                    throw new RpcException(new Status(StatusCode.Unauthenticated, "Invalid or expired token"));
                }

                if (!await ValidatePermissionAsync(context, "servers:execute"))
                {
                    throw new RpcException(new Status(StatusCode.PermissionDenied, "Insufficient permissions"));
                }

                _logger.LogInformation($"Starting GameServer {request.ServerId}");

                // Check if server is already running
                if (_gameServerProcesses.ContainsKey(request.ServerId))
                {
                    var existingProcess = _gameServerProcesses[request.ServerId];
                    if (!existingProcess.HasExited)
                    {
                        return new StartGameServerResponse
                        {
                            Success = false,
                            Message = "Server is already running"
                        };
                    }
                    else
                    {
                        _gameServerProcesses.Remove(request.ServerId);
                    }
                }

                // Build command line arguments
                var args = "--headless";
                if (request.AutoStart)
                {
                    args += " --autostart";
                }
                if (!string.IsNullOrEmpty(request.ConfigPath))
                {
                    args += $" --config=\"{request.ConfigPath}\"";
                }

                // Start AOT process
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "AOT", "HeroYulgang.exe"),
                    Arguments = args,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                var process = Process.Start(processStartInfo);
                if (process == null)
                {
                    return new StartGameServerResponse
                    {
                        Success = false,
                        Message = "Failed to start server process"
                    };
                }

                _gameServerProcesses[request.ServerId] = process;

                // Wait a moment to check if process started successfully
                await Task.Delay(2000);

                if (process.HasExited)
                {
                    var errorOutput = await process.StandardError.ReadToEndAsync();
                    _gameServerProcesses.Remove(request.ServerId);

                    return new StartGameServerResponse
                    {
                        Success = false,
                        Message = $"Server process exited immediately: {errorOutput}"
                    };
                }

                _logger.LogInformation($"GameServer {request.ServerId} started successfully with PID {process.Id}");

                return new StartGameServerResponse
                {
                    Success = true,
                    Message = "Server started successfully",
                    ProcessId = process.Id
                };
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting GameServer {ServerId}", request.ServerId);
                return new StartGameServerResponse
                {
                    Success = false,
                    Message = $"Internal server error: {ex.Message}"
                };
            }
        }

        #endregion

        #region Helper Methods

        private string GenerateAccessToken(AdminUser user)
        {
            var tokenData = $"{user.Username}:{DateTime.UtcNow.Ticks}:{Guid.NewGuid()}";
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(tokenData));
        }

        private string GenerateRefreshToken(AdminUser user)
        {
            var tokenData = $"refresh:{user.Username}:{DateTime.UtcNow.Ticks}:{Guid.NewGuid()}";
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(tokenData));
        }

        private bool ValidateRefreshToken(string refreshToken, out string username)
        {
            username = string.Empty;
            try
            {
                var tokenData = Encoding.UTF8.GetString(Convert.FromBase64String(refreshToken));
                var parts = tokenData.Split(':');
                if (parts.Length >= 2 && parts[0] == "refresh")
                {
                    username = parts[1];
                    return _users.ContainsKey(username);
                }
            }
            catch
            {
                // Invalid token format
            }
            return false;
        }

        private async Task<bool> ValidateTokenAsync(ServerCallContext context)
        {
            var authHeader = context.RequestHeaders.FirstOrDefault(h => h.Key == "authorization");
            if (authHeader == null)
                return false;

            var token = authHeader.Value.Replace("Bearer ", "");

            if (!_activeSessions.ContainsKey(token))
                return false;

            if (_sessionExpiry.TryGetValue(token, out var expiry) && expiry < DateTime.UtcNow)
            {
                _activeSessions.Remove(token);
                _sessionExpiry.Remove(token);
                return false;
            }

            return true;
        }

        private async Task<bool> ValidatePermissionAsync(ServerCallContext context, string requiredPermission)
        {
            var authHeader = context.RequestHeaders.FirstOrDefault(h => h.Key == "authorization");
            if (authHeader == null)
                return false;

            var token = authHeader.Value.Replace("Bearer ", "");

            if (!_activeSessions.TryGetValue(token, out var username))
                return false;

            if (!_users.TryGetValue(username, out var user))
                return false;

            // Check if user has the required permission or wildcard permission
            var resource = requiredPermission.Split(':')[0];
            return user.Permissions.Any(p => p == requiredPermission || p == $"{resource}:*" || p == "*");
        }

        private string GetServerUptime(int serverId)
        {
            if (_gameServerProcesses.TryGetValue(serverId, out var process) && !process.HasExited)
            {
                var uptime = DateTime.Now - process.StartTime;
                return $"{uptime.Days}d {uptime.Hours}h {uptime.Minutes}m";
            }
            return "N/A";
        }

        private double GetCpuUsage()
        {
            // Simple CPU usage calculation (in production, use proper performance counters)
            return Random.Shared.NextDouble() * 100;
        }

        private double GetMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            var totalMemory = GC.GetTotalMemory(false);
            return (double)totalMemory / (1024 * 1024); // MB
        }

        private long GetTotalMemory()
        {
            // Get total system memory (simplified)
            return 8L * 1024 * 1024 * 1024; // 8GB in bytes
        }

        private long GetUsedMemory()
        {
            return GC.GetTotalMemory(false);
        }

        #endregion

        #region Placeholder Methods (to be implemented)

        public override async Task<StopGameServerResponse> StopGameServer(StopGameServerRequest request, ServerCallContext context)
        {
            try
            {
                if (!await ValidateTokenAsync(context))
                {
                    throw new RpcException(new Status(StatusCode.Unauthenticated, "Invalid or expired token"));
                }

                if (!await ValidatePermissionAsync(context, "servers:execute"))
                {
                    throw new RpcException(new Status(StatusCode.PermissionDenied, "Insufficient permissions"));
                }

                if (_gameServerProcesses.TryGetValue(request.ServerId, out var process))
                {
                    if (!process.HasExited)
                    {
                        if (request.Force)
                        {
                            process.Kill();
                        }
                        else
                        {
                            process.CloseMainWindow();
                        }

                        _gameServerProcesses.Remove(request.ServerId);

                        return new StopGameServerResponse
                        {
                            Success = true,
                            Message = "Server stopped successfully"
                        };
                    }
                }

                return new StopGameServerResponse
                {
                    Success = false,
                    Message = "Server is not running"
                };
            }
            catch (RpcException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping GameServer {ServerId}", request.ServerId);
                return new StopGameServerResponse
                {
                    Success = false,
                    Message = "Internal server error"
                };
            }
        }

        // Placeholder implementations for other methods
        public override async Task<RestartGameServerResponse> RestartGameServer(RestartGameServerRequest request, ServerCallContext context)
        {
            // Stop then start
            var stopResult = await StopGameServer(new StopGameServerRequest { ServerId = request.ServerId, Force = request.Force }, context);
            if (!stopResult.Success)
            {
                return new RestartGameServerResponse { Success = false, Message = stopResult.Message };
            }

            await Task.Delay(2000); // Wait before restart

            var startResult = await StartGameServer(new StartGameServerRequest { ServerId = request.ServerId, ConfigPath = request.ConfigPath }, context);
            return new RestartGameServerResponse
            {
                Success = startResult.Success,
                Message = startResult.Message,
                ProcessId = startResult.ProcessId
            };
        }

        #endregion
    }

    // Helper class for admin users
    public class AdminUser
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string[] Roles { get; set; } = Array.Empty<string>();
        public string[] Permissions { get; set; } = Array.Empty<string>();
        public bool IsActive { get; set; } = true;
        public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
    }
}