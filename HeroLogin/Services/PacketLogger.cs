using System;
using System.Text;
using HeroLogin.Core;
using HeroLogin.Core.Actors;

namespace HeroLogin.Services
{
    /// <summary>
    /// Lớp tiện ích để ghi log các gói tin
    /// </summary>
    public static class PacketLogger
    {
        private static readonly ConfigManager _configManager = ConfigManager.Instance;
        
        /// <summary>
        /// Ghi log gói tin nhận được từ client
        /// </summary>
        /// <param name="sessionId">ID của phiên kết nối</param>
        /// <param name="data">Dữ liệu gói tin</param>
        public static void LogIncomingPacket(int sessionId, byte[] data)
        {
            if (_configManager.AppSettings.ShowPacketData && 
                _configManager.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"[PACKET IN] Opcode: {BitConverter.ToUInt16(data, 0)} SessionID: {sessionId} | Length: {data.Length} bytes");
                Console.WriteLine($"HEX: {ByteArrayToHexString(data)}");
                Console.ResetColor();
            }
        }
        
        /// <summary>
        /// Ghi log gói tin gửi đến client
        /// </summary>
        /// <param name="sessionId">ID của phiên kết nối</param>
        /// <param name="data">Dữ liệu gói tin</param>
        public static void LogOutgoingPacket(int sessionId, byte[] data)
        {
            if (_configManager.AppSettings.ShowPacketData && 
                _configManager.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"[PACKET OUT] Opcode: {BitConverter.ToUInt16(data, 0)} SessionID: {sessionId} | Length: {data.Length} bytes");
                Console.WriteLine($"HEX: {ByteArrayToHexString(data)}");
                Console.ResetColor();
            }
        }
        
        /// <summary>
        /// Ghi log gói tin đã được phân tích
        /// </summary>
        /// <param name="sessionId">ID của phiên kết nối</param>
        /// <param name="packet">Gói tin đã được phân tích</param>
        /// <param name="isIncoming">Có phải gói tin nhận vào không</param>
        public static void LogPacket(int sessionId, Packet packet, bool isIncoming)
        {
            if (_configManager.AppSettings.ShowPacketData && 
                _configManager.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                string direction = isIncoming ? "IN" : "OUT";
                ConsoleColor color = isIncoming ? ConsoleColor.Green : ConsoleColor.Yellow;
                
                Console.ForegroundColor = color;
                Console.WriteLine($"[PACKET {direction}] SessionID: {sessionId} | Type: {packet.Type} | Length: {packet.Length} bytes");
                Console.WriteLine($"HEX: {ByteArrayToHexString(packet.Data)}");
                Console.ResetColor();
            }
        }
        
        /// <summary>
        /// Chuyển đổi mảng byte thành chuỗi hex
        /// </summary>
        /// <param name="bytes">Mảng byte cần chuyển đổi</param>
        /// <returns>Chuỗi hex</returns>
        private static string ByteArrayToHexString(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;
                
            var hex = new StringBuilder(bytes.Length * 3);
            foreach (byte b in bytes)
            {
                hex.AppendFormat("{0:X2} ", b);
            }
            
            return hex.ToString().Trim();
        }
    }
}
