using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading;
using System.Threading.Tasks;
using HeroLogin.Core;
using HeroLogin.Core;

namespace HeroLogin.Services
{
    public class GameServerDiscoveryService
    {
        private static GameServerDiscoveryService _instance;
        private static readonly object _lock = new object();
        
        private readonly ConfigManager _configManager;
        private readonly Timer _discoveryTimer;
        private readonly Dictionary<(int clusterId, int serverId), DateTime> _lastPingAttempts;
        private bool _isRunning;

        public static GameServerDiscoveryService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new GameServerDiscoveryService();
                    }
                }
                return _instance;
            }
        }

        private GameServerDiscoveryService()
        {
            _configManager = ConfigManager.Instance;
            _lastPingAttempts = new Dictionary<(int, int), DateTime>();
            
            // Tạo timer để định kỳ tìm kiếm GameServers
            _discoveryTimer = new Timer(DiscoveryCallback, null, Timeout.Infinite, Timeout.Infinite);
        }

        public void Start()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("GameServer Discovery Service đã đang chạy");
                return;
            }

            _isRunning = true;
            Logger.Instance.Info("Khởi động GameServer Discovery Service...");
            
            // Bắt đầu discovery ngay lập tức
            _ = Task.Run(PerformDiscoveryAsync);
            
            // Sau đó chạy định kỳ mỗi 30 giây
            _discoveryTimer.Change(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            Logger.Instance.Info("GameServer Discovery Service đã khởi động");
        }

        public void Stop()
        {
            if (!_isRunning)
                return;

            _isRunning = false;
            _discoveryTimer.Change(Timeout.Infinite, Timeout.Infinite);
            Logger.Instance.Info("GameServer Discovery Service đã dừng");
        }

        private void DiscoveryCallback(object state)
        {
            if (_isRunning)
            {
                _ = Task.Run(PerformDiscoveryAsync);
            }
        }

        private async Task PerformDiscoveryAsync()
        {
            try
            {
                Logger.Instance.Debug("Đang thực hiện discovery GameServers...");
                
                var clusters = _configManager.ServerClusterSettings;
                var clusterManager = ClusterManager.Instance;
                
                foreach (var cluster in clusters)
                {
                    foreach (var channel in cluster.Channels)
                    {
                        var key = (cluster.ID, channel.ServerID);
                        
                        // Kiểm tra xem server đã kết nối chưa
                        var serverChannel = clusterManager.GetServerChannel(cluster.ID, channel.ServerID);
                        if (serverChannel != null && serverChannel.Status)
                        {
                            // Server đã kết nối, bỏ qua
                            continue;
                        }
                        
                        // Kiểm tra thời gian ping cuối cùng để tránh spam
                        if (_lastPingAttempts.TryGetValue(key, out var lastPing))
                        {
                            if (DateTime.Now - lastPing < TimeSpan.FromSeconds(10))
                            {
                                continue; // Chưa đến lúc ping lại
                            }
                        }
                        
                        _lastPingAttempts[key] = DateTime.Now;
                        
                        // Thử ping GameServer
                        bool isReachable = await PingGameServerAsync(channel.ServerIP, channel.GameServerPort);
                        
                        if (isReachable)
                        {
                            Logger.Instance.Info($"Phát hiện GameServer {channel.ServerName} tại {channel.ServerIP}:{channel.GameServerPort}");
                            
                            // Gửi thông báo để GameServer kết nối (nếu cần)
                            await NotifyGameServerAsync(channel);
                        }
                        else
                        {
                            Logger.Instance.Debug($"GameServer {channel.ServerName} chưa sẵn sàng tại {channel.ServerIP}:{channel.GameServerPort}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong quá trình discovery GameServers: {ex.Message}");
            }
        }

        private async Task<bool> PingGameServerAsync(string serverIP, int port)
        {
            try
            {
                using (var ping = new Ping())
                {
                    var reply = await ping.SendPingAsync(serverIP, 3000);
                    return reply.Status == IPStatus.Success;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Debug($"Ping failed for {serverIP}: {ex.Message}");
                return false;
            }
        }

        private async Task NotifyGameServerAsync(ServerChannel channel)
        {
            try
            {
                // Ở đây có thể gửi một signal hoặc thông báo đến GameServer
                // Hiện tại chỉ log, GameServer sẽ tự động kết nối khi khởi động
                Logger.Instance.Info($"GameServer {channel.ServerName} có thể kết nối được, đang chờ đăng ký...");
                
                // Có thể thêm logic để gửi HTTP request hoặc UDP packet đến GameServer
                // để thông báo LoginServer đã sẵn sàng
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi thông báo đến GameServer {channel.ServerName}: {ex.Message}");
            }
        }

        public async Task ForceDiscoveryAsync()
        {
            Logger.Instance.Info("Thực hiện force discovery GameServers...");
            await PerformDiscoveryAsync();
        }

        public void Dispose()
        {
            Stop();
            _discoveryTimer?.Dispose();
        }
    }
}
