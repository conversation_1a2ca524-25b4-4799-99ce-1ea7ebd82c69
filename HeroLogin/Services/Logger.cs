using System;
using System.IO;

namespace HeroLogin.Services
{
    /// <summary>
    /// Lớp Logger đơn giản để ghi log
    /// </summary>
    public class Logger
    {
        private static Logger? _instance;
        private readonly string _logFilePath;
        private readonly object _lockObj = new object();

        public static Logger Instance => _instance ??= new Logger();

        private Logger()
        {
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            _logFilePath = Path.Combine(logDirectory, $"HeroLogin_{DateTime.Now:yyyyMMdd}.log");
        }

        public void Info(string message)
        {
            WriteLog("INFO", message);
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine($"[INFO] {message}");
            Console.ResetColor();
        }

        public void Debug(string message)
        {
            WriteLog("DEBUG", message);
            Console.ForegroundColor = ConsoleColor.Gray;
            Console.WriteLine($"[DEBUG] {message}");
            Console.ResetColor();
        }

        public void Warning(string message)
        {
            WriteLog("WARNING", message);
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"[WARNING] {message}");
            Console.ResetColor();
        }

        public void Error(string message)
        {
            WriteLog("ERROR", message);
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"[ERROR] {message}");
            Console.ResetColor();
        }

        private void WriteLog(string level, string message)
        {
            try
            {
                lock (_lockObj)
                {
                    using StreamWriter writer = File.AppendText(_logFilePath);
                    writer.WriteLine($"{DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] {message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi ghi log: {ex.Message}");
            }
        }
    }
}
