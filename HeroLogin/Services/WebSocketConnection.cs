using System;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace HeroLogin.Services
{
    public class WebSocketConnection
    {
        private readonly WebSocket _webSocket;
        private readonly HttpContext _context;
        private readonly CancellationTokenSource _cancellationTokenSource;

        public string ConnectionId { get; }
        public string? PlayerId { get; set; }
        public DateTime ConnectedAt { get; }

        public WebSocketConnection(string connectionId, WebSocket webSocket, HttpContext context)
        {
            ConnectionId = connectionId;
            _webSocket = webSocket;
            _context = context;
            _cancellationTokenSource = new CancellationTokenSource();
            ConnectedAt = DateTime.Now;
        }

        public async Task StartListening(Func<WebSocketConnection, string, Task> messageHandler)
        {
            var buffer = new byte[4096];

            try
            {
                while (_webSocket.State == WebSocketState.Open && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var result = await _webSocket.ReceiveAsync(
                        new ArraySegment<byte>(buffer), 
                        _cancellationTokenSource.Token);

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        await messageHandler(this, message);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await _webSocket.CloseAsync(
                            WebSocketCloseStatus.NormalClosure, 
                            "Connection closed by client", 
                            _cancellationTokenSource.Token);
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Connection was cancelled, this is expected
            }
            catch (WebSocketException ex)
            {
                Logger.Instance.Warning($"WebSocket exception in connection {ConnectionId}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Unexpected error in WebSocket connection {ConnectionId}: {ex.Message}");
            }
        }

        public async Task SendAsync(string message)
        {
            if (_webSocket.State != WebSocketState.Open)
                return;

            try
            {
                var buffer = Encoding.UTF8.GetBytes(message);
                await _webSocket.SendAsync(
                    new ArraySegment<byte>(buffer),
                    WebSocketMessageType.Text,
                    true,
                    _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi tin nhắn qua WebSocket {ConnectionId}: {ex.Message}");
            }
        }

        public async Task SendErrorAsync(string message, string action)
        {
            var error = new Dictionary<string, object>
            {
                { "action", action },
                { "valid", false },
                { "message", message }
            };

            await SendAsync(JsonConvert.SerializeObject(error));
        }

        public async Task SendResponseAsync(Dictionary<string, object> response)
        {
            await SendAsync(JsonConvert.SerializeObject(response));
        }

        public void Close()
        {
            try
            {
                _cancellationTokenSource.Cancel();
                
                if (_webSocket.State == WebSocketState.Open)
                {
                    _webSocket.CloseAsync(
                        WebSocketCloseStatus.NormalClosure, 
                        "Server shutdown", 
                        CancellationToken.None).Wait(TimeSpan.FromSeconds(5));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi đóng WebSocket connection {ConnectionId}: {ex.Message}");
            }
            finally
            {
                _cancellationTokenSource.Dispose();
            }
        }

        public string GetClientInfo()
        {
            return $"{_context.Connection.RemoteIpAddress}:{_context.Connection.RemotePort}";
        }
    }
}
