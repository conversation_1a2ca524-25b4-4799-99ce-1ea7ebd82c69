using System;

namespace HeroLogin.Database.FreeSql.Common
{
    /// <summary>
    /// Generic result wrapper for service operations
    /// Provides consistent error handling and success/failure indication
    /// </summary>
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; private set; }
        public T? Data { get; private set; }
        public string? ErrorMessage { get; private set; }
        public Exception? Exception { get; private set; }
        public DateTime Timestamp { get; private set; }

        private ServiceResult(bool isSuccess, T? data, string? errorMessage, Exception? exception)
        {
            IsSuccess = isSuccess;
            Data = data;
            ErrorMessage = errorMessage;
            Exception = exception;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Create successful result with data
        /// </summary>
        public static ServiceResult<T> Success(T data)
        {
            return new ServiceResult<T>(true, data, null, null);
        }

        /// <summary>
        /// Create successful result without data
        /// </summary>
        public static ServiceResult<T> Success()
        {
            return new ServiceResult<T>(true, default, null, null);
        }

        /// <summary>
        /// Create failed result with error message
        /// </summary>
        public static ServiceResult<T> Failure(string errorMessage)
        {
            return new ServiceResult<T>(false, default, errorMessage, null);
        }

        /// <summary>
        /// Create failed result with exception
        /// </summary>
        public static ServiceResult<T> Failure(Exception exception)
        {
            return new ServiceResult<T>(false, default, exception.Message, exception);
        }

        /// <summary>
        /// Create failed result with error message and exception
        /// </summary>
        public static ServiceResult<T> Failure(string errorMessage, Exception exception)
        {
            return new ServiceResult<T>(false, default, errorMessage, exception);
        }

        /// <summary>
        /// Implicit conversion to bool (for easy success checking)
        /// </summary>
        public static implicit operator bool(ServiceResult<T> result)
        {
            return result.IsSuccess;
        }

        /// <summary>
        /// Get data or throw exception if failed
        /// </summary>
        public T GetDataOrThrow()
        {
            if (!IsSuccess)
            {
                throw Exception ?? new InvalidOperationException(ErrorMessage ?? "Operation failed");
            }
            return Data!;
        }

        /// <summary>
        /// Get data or return default value if failed
        /// </summary>
        public T? GetDataOrDefault(T? defaultValue = default)
        {
            return IsSuccess ? Data : defaultValue;
        }
    }

    /// <summary>
    /// Non-generic service result for operations that don't return data
    /// </summary>
    public class ServiceResult
    {
        public bool IsSuccess { get; private set; }
        public string? ErrorMessage { get; private set; }
        public Exception? Exception { get; private set; }
        public DateTime Timestamp { get; private set; }

        private ServiceResult(bool isSuccess, string? errorMessage, Exception? exception)
        {
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
            Exception = exception;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// Create successful result
        /// </summary>
        public static ServiceResult Success()
        {
            return new ServiceResult(true, null, null);
        }

        /// <summary>
        /// Create failed result with error message
        /// </summary>
        public static ServiceResult Failure(string errorMessage)
        {
            return new ServiceResult(false, errorMessage, null);
        }

        /// <summary>
        /// Create failed result with exception
        /// </summary>
        public static ServiceResult Failure(Exception exception)
        {
            return new ServiceResult(false, exception.Message, exception);
        }
        
        /// <summary>
        /// Create failed result with exception
        /// </summary>
        public static ServiceResult Failure(string errorMessage,Exception exception)
        {
            return new ServiceResult(false, errorMessage, exception);
        }



        /// <summary>
        /// Implicit conversion to bool (for easy success checking)
        /// </summary>
        public static implicit operator bool(ServiceResult result)
        {
            return result.IsSuccess;
        }

        /// <summary>
        /// Throw exception if failed
        /// </summary>
        public void ThrowIfFailed()
        {
            if (!IsSuccess)
            {
                throw Exception ?? new InvalidOperationException(ErrorMessage ?? "Operation failed");
            }
        }
    }
}
