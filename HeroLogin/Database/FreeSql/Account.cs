
using System;
using System.Threading.Tasks;
using FreeSql;
using HeroLogin.Core;
using HeroLogin.Database.FreeSql.Entities.Account;

namespace HeroLogin.Database.FreeSql;

public static class AccountDb
{
    private static IFreeSql? _freeSql;

    public static bool InitializeAsync()
    {
        try
        {
            if (_freeSql != null) return true;

            var connectionString = ConfigManager.Instance.PogresSettings.AccountDb;
            // Console.WriteLine("AccountDb connection string: " + connectionString);
            // Create FreeSql instance
            _freeSql = new FreeSqlBuilder()
                .UseConnectionString(DataType.PostgreSQL, connectionString)
                .UseAutoSyncStructure(false)
                // .UseNoneCommandParameter(true)
                .Build();

            Console.WriteLine("✓ AccountDb initialized successfully");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Failed to initialize AccountDb: {ex.Message}");
            return false;
        }
    }

    public async static Task UpdatePasskey(string accountid, string passkey)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_passkey == passkey)
            .Set(a => a.fld_passkey_timestamp == DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating Passkey " + ex.Message);
        }
    }


    public async static Task UpdateLastLoginTime(string accountid)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_lastlogintime == DateTime.Now)
            .Set(a => a.fld_online == 1)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating Last Login Time " + ex.Message);
        }
    }


    public static void RefreshCheckLogin()
    {
        try
        {
            if (_freeSql == null) return;
            _freeSql?.Update<account>().Set(a => a.fld_checklogin, false).Set(a => a.fld_checkip, null).Where(a => a.fld_checklogin == true).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Failed to refresh check login: {ex.Message}");
        }
    }

    public async static void UpdateRefreshKey(string accountid, string refreshkey)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_refresh_key == refreshkey)
            .Set(a => a.fld_refresh_key_timestamp == DateTime.Now)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating Refresh Key " + ex.Message);
        }
    }

    public async static void UpdateRemoveUserLogin(string accountid)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_online == 0)
            .Set(a => a.fld_checklogin == false)
            .Set(a => a.fld_checkip == null)
            .Set(a => a.fld_lanip == null)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Updating User Login " + ex.Message);
        }
    }

    public async static void BanAnAccount(string accountid, string reason, int hour)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_ghichu == reason)
            .Set(a => a.fld_zt == hour)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Banning Account " + ex.Message);
        }
    }
    public async static void UnbanAnAccount(string accountid)
    {
        try
        {
            if (_freeSql == null) return;
            await _freeSql
            .Update<account>()
            .Set(a => a.fld_ghichu == null)
            .Set(a => a.fld_zt == 0)
            .Where(a => a.fld_id == accountid)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Unbanning Account " + ex.Message);
        }
    }

    public async static Task<account> FindAccount(string accountid)
    {
        try
        {
            if (_freeSql == null) return null;
            var account = await _freeSql
            .Select<account>()
            .Where(a => a.fld_id == accountid)
            .FirstAsync();
            return account;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error Loading Account Cash " + ex.Message);
            return null;
        }
    }

    public async static Task UpdateServerStatus(string serverName, int serverId, int currentPlayers, int maxPlayers)
    {
        try
        {
            // Check if server status exists
            var existingStatus = await _freeSql
                .Select<tbl_online>()
                .Where(o => o.fld_server == serverName && o.fld_zone == serverId)
                .FirstAsync();

            if (existingStatus == null)
            {
                // Create new server status
                await _freeSql
                    .Insert<tbl_online>()
                    .AppendData(new tbl_online
                    {
                        fld_server = serverName,
                        fld_zone = serverId,
                        fld_nowuser = currentPlayers,
                        fld_maxuser = maxPlayers
                    })
                    .ExecuteAffrowsAsync();
            }
            else
            {
                // Update existing server status
                await _freeSql
                    .Update<tbl_online>()
                    .Set(o => o.fld_nowuser == currentPlayers)
                    .Set(o => o.fld_maxuser == maxPlayers)
                    .Where(o => o.fld_server == serverName && o.fld_zone == serverId)
                    .ExecuteAffrowsAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating server status: {ex.Message}");
        }
    }

}
