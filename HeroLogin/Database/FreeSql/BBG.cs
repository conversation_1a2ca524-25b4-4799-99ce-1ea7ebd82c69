

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FreeSql;
using HeroLogin.Core;
using HeroLogin.Services;
using HeroLogin.Database.FreeSql.Entities.BBG;
using RxjhServer;

namespace HeroLogin.Database.FreeSql
{
    public static class BBGDb
    {
        private static IFreeSql _freeSql;


        public static bool Initialize()
        {
            try
            {
                if (_freeSql != null) return true;

                var connectionString = ConfigManager.Instance.PogresSettings.BBGDb;
                Logger.Instance.Error("BBGDb connection string: " + connectionString);
                // Create FreeSql instance
                _freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false)
                    // .UseNoneCommandParameter(true)
                    .Build();

                Logger.Instance.Error("✓ BBGDb initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to initialize BBGDb: {ex.Message}");
                return false;
            }

        }
    }
}


