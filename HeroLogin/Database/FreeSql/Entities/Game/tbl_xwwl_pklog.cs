﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_pklog {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_pklog_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_killer { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_killer_guild { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_death { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_death_guild { get; set; }

		[JsonProperty]
		public int? fld_num { get; set; }

		[JsonProperty]
		public int? fld_wx { get; set; }

		[JsonProperty]
		public DateTime? fld_lasttime { get; set; }

	}

}
