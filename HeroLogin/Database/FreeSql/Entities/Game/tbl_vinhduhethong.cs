﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_vinhduhethong {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_vinhduhethong_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty]
		public int? fld_nghenghiep { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_tennhanvat { get; set; }

		[JsonProperty]
		public int? fld_dangcap { get; set; }

		[JsonProperty]
		public int? fld_theluc { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_bangphai { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_bangphai_bangchu { get; set; }

		[JsonProperty]
		public int? fld_diemso { get; set; }

	}

}
