﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_group_quest_history {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_group_quest_history_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty]
		public int? questid { get; set; }

		[JsonProperty]
		public int? guildid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string guildname { get; set; }

		[JsonProperty]
		public int? factionid { get; set; }

		[JsonProperty]
		public DateTime? completedtime { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string completedby { get; set; }

	}

}
