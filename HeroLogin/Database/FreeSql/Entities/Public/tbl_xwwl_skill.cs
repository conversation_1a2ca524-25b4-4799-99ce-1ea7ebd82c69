﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_skill {

		[JsonProperty]
		public int? fld_id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public double? fld_bonusratevalueperpoint1 { get; set; }

		[JsonProperty]
		public double? fld_bonusratevalueperpoint2 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_des { get; set; }

	}

}
