﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_monster {

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_hp { get; set; }

		[JsonProperty]
		public int? fld_at { get; set; }

		[JsonProperty]
		public int? fld_df { get; set; }

		[JsonProperty]
		public int? fld_exp { get; set; }

		[JsonProperty]
		public int? fld_boss { get; set; }

		[JsonProperty]
		public int? fld_auto { get; set; }

		[JsonProperty]
		public int? fld_npc { get; set; }

		[JsonProperty]
		public int? fld_quest { get; set; }

		[JsonProperty]
		public int? fld_questid { get; set; }

		[JsonProperty]
		public int? fld_stages { get; set; }

		[JsonProperty]
		public int? fld_questitem { get; set; }

		[JsonProperty]
		public int? fld_pp { get; set; }

	}

}
