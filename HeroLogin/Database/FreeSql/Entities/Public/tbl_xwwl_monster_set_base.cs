﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_monster_set_base {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_monster_set_base_fld_index_seq'::regclass)")]
		public int fld_index { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty]
		public double? fld_x { get; set; }

		[JsonProperty]
		public double? fld_z { get; set; }

		[JsonProperty]
		public double? fld_y { get; set; }

		[JsonProperty]
		public double? fld_face0 { get; set; }

		[JsonProperty]
		public double? fld_face { get; set; }

		[JsonProperty]
		public int? fld_mid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public long? fld_hp { get; set; }

		[JsonProperty]
		public long? fld_at { get; set; }

		[JsonProperty]
		public long? fld_df { get; set; }

		[JsonProperty]
		public int? fld_npc { get; set; }

		[JsonProperty]
		public int? fld_newtime { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_exp { get; set; }

		[JsonProperty]
		public int? fld_auto { get; set; }

		[JsonProperty]
		public int? fld_boss { get; set; }

		[JsonProperty]
		public int? fld_gold { get; set; }

		[JsonProperty]
		public int? fld_accuracy { get; set; }

		[JsonProperty]
		public int? fld_evasion { get; set; }

		[JsonProperty]
		public int? fld_qitemdrop { get; set; }

		[JsonProperty]
		public int? fld_qdroppp { get; set; }

		[JsonProperty]
		public int? fld_freedrop { get; set; }

		[JsonProperty]
		public int? fld_amount { get; set; }

		[JsonProperty]
		public int? fld_aoe { get; set; }

		[JsonProperty]
		public int? fld_active { get; set; }

	}

}
