﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroLogin.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class giftcodelog {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('giftcodelog_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string character { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string code { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string reward { get; set; }

		[JsonProperty]
		public int? status { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? expired_at { get; set; }

	}

}
