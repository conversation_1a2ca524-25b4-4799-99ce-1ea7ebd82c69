using System;

namespace HeroLogin.Database.FreeSql.Models
{
    /// <summary>
    /// DCH Player Ranking Information
    /// Thông tin xếp hạng người chơi DCH
    /// </summary>
    public class DCHPlayerRankingInfo
    {
        /// <summary>
        /// Player name
        /// Tên người chơi
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// Player rank (1-20)
        /// Xếp hạng người chơi (1-20)
        /// </summary>
        public int Rank { get; set; }

        /// <summary>
        /// Damage to towers
        /// Sát thương lên trụ
        /// </summary>
        public long DameTru { get; set; }

        /// <summary>
        /// Number of kills
        /// Số lượng giết người
        /// </summary>
        public int GietNguoiSoLuong { get; set; }

        /// <summary>
        /// Player level
        /// Đẳng cấp người chơi
        /// </summary>
        public int DangCap { get; set; }

        /// <summary>
        /// WuXun reward amount
        /// Số lượng võ huân thưởng
        /// </summary>
        public int WuXunReward { get; set; }

        /// <summary>
        /// Item reward ID
        /// ID vật phẩm thưởng
        /// </summary>
        public int ItemReward { get; set; }

        /// <summary>
        /// Reward message to display
        /// Thông báo phần thưởng hiển thị
        /// </summary>
        public string RewardMessage { get; set; }
    }

    /// <summary>
    /// DCH Reward Information
    /// Thông tin phần thưởng DCH
    /// </summary>
    public class DCHRewardInfo
    {
        /// <summary>
        /// WuXun reward amount
        /// Số lượng võ huân thưởng
        /// </summary>
        public int WuXunReward { get; set; }

        /// <summary>
        /// Item reward ID
        /// ID vật phẩm thưởng
        /// </summary>
        public int ItemReward { get; set; }

        /// <summary>
        /// Reward message to display
        /// Thông báo phần thưởng hiển thị
        /// </summary>
        public string RewardMessage { get; set; }
    }
}
