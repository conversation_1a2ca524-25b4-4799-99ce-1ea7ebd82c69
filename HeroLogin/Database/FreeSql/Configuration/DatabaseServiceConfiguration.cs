using System;

namespace HeroLogin.Database.FreeSql.Configuration
{
    /// <summary>
    /// Configuration for different database service strategies
    /// </summary>
    public class DatabaseServiceConfiguration
    {
        public TemplateDataConfig TemplateData { get; set; } = new();
        public AccountConfig Account { get; set; } = new();
        public PlayerDataConfig PlayerData { get; set; } = new();
        public MarketplaceConfig Marketplace { get; set; } = new();
    }

    /// <summary>
    /// Configuration for Template Data Service (PublicDb)
    /// Strategy: Cache once, read forever
    /// </summary>
    public class TemplateDataConfig
    {
        /// <summary>
        /// Cache expiration time for template data (default: never expire)
        /// </summary>
        public TimeSpan CacheExpiration { get; set; } = TimeSpan.MaxValue;

        /// <summary>
        /// Enable cache refresh via admin commands
        /// </summary>
        public bool EnableCacheRefresh { get; set; } = true;

        /// <summary>
        /// Preload all templates on startup
        /// </summary>
        public bool PreloadOnStartup { get; set; } = true;

        /// <summary>
        /// Cache size limit (number of items)
        /// </summary>
        public int CacheSizeLimit { get; set; } = 100000;
    }

    /// <summary>
    /// Configuration for Account Service (AccountDb)
    /// Strategy: Simple direct operations
    /// </summary>
    public class AccountConfig
    {
        /// <summary>
        /// Connection timeout for account operations
        /// </summary>
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Enable account data caching (short-term)
        /// </summary>
        public bool EnableCaching { get; set; } = false;

        /// <summary>
        /// Cache duration for account data
        /// </summary>
        public TimeSpan CacheDuration { get; set; } = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// Configuration for Player Data Service (GameDb)
    /// Strategy: Direct read, batched write
    /// </summary>
    public class PlayerDataConfig
    {
        /// <summary>
        /// Batch write interval
        /// </summary>
        public TimeSpan BatchWriteInterval { get; set; } = TimeSpan.FromSeconds(5);

        /// <summary>
        /// Maximum batch size before forcing flush
        /// </summary>
        public int MaxBatchSize { get; set; } = 1000;

        /// <summary>
        /// Queue capacity for write operations
        /// </summary>
        public int WriteQueueCapacity { get; set; } = 10000;

        /// <summary>
        /// Enable emergency flush on high queue size
        /// </summary>
        public bool EnableEmergencyFlush { get; set; } = true;

        /// <summary>
        /// Emergency flush threshold (percentage of queue capacity)
        /// </summary>
        public double EmergencyFlushThreshold { get; set; } = 0.8;

        /// <summary>
        /// Connection timeout for read operations
        /// </summary>
        public TimeSpan ReadTimeout { get; set; } = TimeSpan.FromSeconds(10);

        /// <summary>
        /// Connection timeout for write operations
        /// </summary>
        public TimeSpan WriteTimeout { get; set; } = TimeSpan.FromSeconds(30);
    }

    /// <summary>
    /// Configuration for Marketplace Service (BBGDb)
    /// Strategy: Queue with distributed locking
    /// </summary>
    public class MarketplaceConfig
    {
        /// <summary>
        /// Operation queue processing interval
        /// </summary>
        public TimeSpan ProcessingInterval { get; set; } = TimeSpan.FromMilliseconds(100);

        /// <summary>
        /// Maximum queue size for marketplace operations
        /// </summary>
        public int MaxQueueSize { get; set; } = 5000;

        /// <summary>
        /// Distributed lock timeout
        /// </summary>
        public TimeSpan LockTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Redis connection string for distributed locking
        /// </summary>
        public string? RedisConnectionString { get; set; }

        /// <summary>
        /// Enable distributed locking (requires Redis)
        /// </summary>
        public bool EnableDistributedLocking { get; set; } = true;

        /// <summary>
        /// Search result cache duration
        /// </summary>
        public TimeSpan SearchCacheDuration { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Maximum concurrent operations
        /// </summary>
        public int MaxConcurrentOperations { get; set; } = 10;

        /// <summary>
        /// Operation timeout
        /// </summary>
        public TimeSpan OperationTimeout { get; set; } = TimeSpan.FromSeconds(60);

        /// <summary>
        /// Retry count for failed operations
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// Retry delay
        /// </summary>
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
    }
}
