using System.Collections.Generic;
using FreeSql;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace HeroLogin.Database.FreeSql.Configuration
{
    /// <summary>
    /// FreeSql configuration for PostgreSQL databases
    /// AOT-compatible configuration without reflection
    /// </summary>
    public static class FreeSqlConfiguration
    {
        private static readonly Dictionary<string, IFreeSql> _instances = new();
        private static readonly object _lock = new object();

        /// <summary>
        /// Create FreeSql instance for PublicDb (Template data)
        /// </summary>
        public static IFreeSql CreatePublicDbInstance(string connectionString, ILogger? logger = null)
        {
            lock (_lock)
            {
                if (_instances.TryGetValue("PublicDb", out var existingInstance))
                    return existingInstance;

                var freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false) // Disable for production
                    .UseNoneCommandParameter(true) // Better for AOT
                    .UseMonitorCommand(cmd =>
                    {
                        logger?.LogDebug("PublicDb SQL: {Sql}", cmd.CommandText);
                    })
                    .Build();

                ConfigureForAot(freeSql);
                _instances["PublicDb"] = freeSql;
                return freeSql;
            }
        }

        /// <summary>
        /// Create FreeSql instance for AccountDb
        /// </summary>
        public static IFreeSql CreateAccountDbInstance(string connectionString, ILogger? logger = null)
        {
            lock (_lock)
            {
                if (_instances.TryGetValue("AccountDb", out var existingInstance))
                    return existingInstance;

                var freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false)
                    .UseNoneCommandParameter(true)
                    .UseMonitorCommand(cmd =>
                    {
                        logger?.LogDebug("AccountDb SQL: {Sql}", cmd.CommandText);
                    })
                    .Build();

                ConfigureForAot(freeSql);
                _instances["AccountDb"] = freeSql;
                return freeSql;
            }
        }

        /// <summary>
        /// Create FreeSql instance for GameDb (High frequency operations)
        /// </summary>
        public static IFreeSql CreateGameDbInstance(string connectionString, ILogger? logger = null)
        {
            lock (_lock)
            {
                if (_instances.TryGetValue("GameDb", out var existingInstance))
                    return existingInstance;

                var freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false)
                    .UseNoneCommandParameter(true)
                    .UseAdoConnectionPool(true) // Enable connection pooling for high frequency
                    .UseMonitorCommand(cmd =>
                    {
                        logger?.LogDebug("GameDb SQL: {Sql}", cmd.CommandText);
                    })
                    .Build();

                ConfigureForAot(freeSql);
                _instances["GameDb"] = freeSql;
                return freeSql;
            }
        }

        /// <summary>
        /// Create FreeSql instance for BBGDb (Marketplace with high concurrency)
        /// </summary>
        public static IFreeSql CreateBBGDbInstance(string connectionString, ILogger? logger = null)
        {
            lock (_lock)
            {
                if (_instances.TryGetValue("BBGDb", out var existingInstance))
                    return existingInstance;

                var freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    .UseAutoSyncStructure(false)
                    .UseNoneCommandParameter(true)
                    .UseAdoConnectionPool(true)
                    .UseMonitorCommand(cmd =>
                    {
                        logger?.LogDebug("BBGDb SQL: {Sql}", cmd.CommandText);
                    })
                    .Build();

                ConfigureForAot(freeSql);
                _instances["BBGDb"] = freeSql;
                return freeSql;
            }
        }

        /// <summary>
        /// Configure FreeSql for AOT compatibility
        /// </summary>
        private static void ConfigureForAot(IFreeSql freeSql)
        {
            // Disable reflection-based features for AOT compatibility
            freeSql.GlobalFilter.Apply<object>("SoftDelete", a => true);
            
            // Pre-compile common expressions to avoid runtime compilation
            // This will be expanded as we add more entities
        }

        /// <summary>
        /// Get existing instance by database name
        /// </summary>
        public static IFreeSql? GetInstance(string databaseName)
        {
            lock (_lock)
            {
                return _instances.TryGetValue(databaseName, out var instance) ? instance : null;
            }
        }

        /// <summary>
        /// Dispose all instances (for application shutdown)
        /// </summary>
        public static void DisposeAll()
        {
            lock (_lock)
            {
                foreach (var instance in _instances.Values)
                {
                    instance?.Dispose();
                }
                _instances.Clear();
            }
        }
    }
}
