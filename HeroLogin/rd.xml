<?xml version="1.0" encoding="utf-8"?>
<Directives xmlns="http://schemas.microsoft.com/netfx/2013/01/metadata">
  <Application>
    <!-- Preserve Akka.NET core assemblies -->
    <Assembly Name="Akka" Dynamic="Required All" />
    <Assembly Name="Akka.Remote" Dynamic="Required All" />
    <Assembly Name="Akka.Streams" Dynamic="Required All" />

    <!-- Preserve our Actor classes -->
    <Assembly Name="HeroLogin">
      <Type Name="HeroLogin.Core.Actors.TcpManagerActor" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.Actors.PacketHandlerActor" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.Actors.ActorSystemManager" Dynamic="Required All" />

      <!-- Preserve message types -->
      <Type Name="HeroLogin.Core.Actors.SetTcpManagerActor" Dynamic="Required All" />

      <!-- Preserve Configuration classes -->
      <Type Name="HeroLogin.Core.ConfigManager" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.ServerChannel" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.ServerCluster" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.LoginServerSettings" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.AppSettings" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.DatabaseConfig" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.PogresConfig" Dynamic="Required All" />

      <!-- Preserve FreeSql Entity Classes for AOT -->
      <Type Name="HeroLogin.Database.FreeSql.Entities.BBG.shopcategory" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.BBG.cashshop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.BBG.mailcod" Dynamic="Required All" />


      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_item" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_monster" Dynamic="Required All" />
      <!-- Additional FreeSql Entity Classes for AOT -->
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_monster_set_base" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_drop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_drop_gs" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_drop_dch" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_bossdrop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_itemoption" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_open" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_kongfu" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_sell" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_vome" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_gg" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.dangcapbanthuong" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.vatphamtraodoi" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_map" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.xwwl_kill" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.kiemtrathietbi" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.thangthienkhicong" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_skill" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_stone" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.giftcode" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.giftcode_rewards" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_upgrade_item" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.cheduocvatphamdanhsach" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.chetacvatphamdanhsach" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_mission" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.BBG.itmeclss" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_npc" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Public.tbl_xwwl_opn" Dynamic="Required All" />


      <!-- Account Entity Classes -->
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.account" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.addcash" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.banned" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.doanhthu" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.ipcheck" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.tbl_more_run" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.tbl_online" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.tbl_trucash" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Account.tbl_updatelog" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.BBG.cash_shop_log" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_history" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_contribution" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_contribution_log" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_guild_quest_progress" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_cw" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.bangchien_tiendatcuoc" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.congthanhchien_thanhchu" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.thienmathancung_danhsach" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_guild" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_guildmember" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.vinhdubangphaixephang" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_vinhduhethong" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.bachbaocacrecord" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.drugrecord" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.eventtop_dch" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.eventtop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.exchangecharacter" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.giftcode" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.itemrecord" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.log_deleteitem" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.log_thelucchien" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.loginrecord_mac" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.loginrecord" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.logpk" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.logshop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.logshopvohuan" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.syntheticrecord" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_faction_quest_progress" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_contribution_log" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_contribution" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest_history" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_group_quest" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_guild_quest_progress" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_sudosolieu" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_truyenthuhethong" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_vinhduhethong" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_char" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_cw" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_cwarehouse" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_pklog" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_publicwarehouse" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_pvp" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_rosetop" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.tbl_xwwl_warehouse" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.thienmathancung_danhsach" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.v_synthesis_summary" Dynamic="Required All" />
      <Type Name="HeroLogin.Database.FreeSql.Entities.Game.xwwl_gameserverinfo" Dynamic="Required All" />

      <!-- Logger and Services -->
      <Type Name="HeroLogin.Services.Logger" Dynamic="Required All" />
      <Type Name="HeroLogin.Services.LoginAuthService" Dynamic="Required All" />
      <Type Name="HeroLogin.Services.WebAdminService" Dynamic="Required All" />
      <Type Name="HeroLogin.Services.WebSocketService" Dynamic="Required All" />
      <Type Name="HeroLogin.Services.GameServerDiscoveryService" Dynamic="Required All" />

      <!-- Core classes -->
      <Type Name="HeroLogin.Core.LoginServer" Dynamic="Required All" />
      <Type Name="HeroLogin.Core.ClusterManager" Dynamic="Required All" />

    </Assembly>
  </Application>
</Directives>
