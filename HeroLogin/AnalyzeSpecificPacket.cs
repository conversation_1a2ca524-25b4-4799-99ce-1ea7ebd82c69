using System;
using System.Text;

namespace HeroLogin.Tests
{
    public class AnalyzeSpecificPacket
    {
        // Packet từ client: 00801b00010031010031040000f6c4f9010030000000000000000000000000
        private static readonly string PacketHex = "00801b00010031010031040000f6c4f9010030000000000000000000000000";
        
        public static void RunAnalysis()
        {
            Console.WriteLine("=== Bắt đầu phân tích packet cụ thể ===");
            
            // Chuyển đổi chuỗi hex thành mảng byte
            byte[] packetData = HexStringToByteArray(PacketHex);
            
            Console.WriteLine($"Packet data length: {packetData.Length} bytes");
            Console.WriteLine($"Packet data: {BitConverter.ToString(packetData).Replace("-", " ")}");
            
            // Phân tích header
            if (packetData.Length >= 4)
            {
                ushort opcode = BitConverter.ToUInt16(packetData, 0);
                ushort length = BitConverter.ToUInt16(packetData, 2);
                
                Console.WriteLine($"Opcode: {opcode} (0x{opcode:X4})");
                Console.WriteLine($"Length: {length}");
                
                // Phân tích dữ liệu
                if (packetData.Length >= 4 + length)
                {
                    byte[] data = new byte[length];
                    Array.Copy(packetData, 4, data, 0, length);
                    
                    Console.WriteLine($"Data: {BitConverter.ToString(data).Replace("-", " ")}");
                    
                    // Phân tích chi tiết từng byte trong dữ liệu
                    Console.WriteLine("\nPhân tích chi tiết dữ liệu:");
                    for (int i = 0; i < data.Length; i++)
                    {
                        Console.WriteLine($"Byte {i}: {data[i]} (0x{data[i]:X2})");
                    }
                    
                    // Thử phân tích dữ liệu theo các cách khác nhau
                    Console.WriteLine("\nPhân tích dữ liệu theo các cách khác nhau:");
                    
                    // Phân tích theo cấu trúc đăng nhập
                    Console.WriteLine("\nPhân tích theo cấu trúc đăng nhập:");
                    try
                    {
                        if (data.Length >= 2)
                        {
                            short usernameLength = BitConverter.ToInt16(data, 0);
                            Console.WriteLine($"Username length: {usernameLength}");
                            
                            if (data.Length >= 2 + usernameLength)
                            {
                                string username = Encoding.Default.GetString(data, 2, usernameLength);
                                Console.WriteLine($"Username: {username}");
                                
                                if (data.Length >= 2 + usernameLength + 2)
                                {
                                    short passwordLength = BitConverter.ToInt16(data, 2 + usernameLength);
                                    Console.WriteLine($"Password length: {passwordLength}");
                                    
                                    if (data.Length >= 2 + usernameLength + 2 + passwordLength)
                                    {
                                        string password = Encoding.Default.GetString(data, 4 + usernameLength, passwordLength);
                                        Console.WriteLine($"Password: {password}");
                                        
                                        // Hiển thị các byte còn lại
                                        int parsedLength = 2 + usernameLength + 2 + passwordLength;
                                        if (data.Length > parsedLength)
                                        {
                                            byte[] remainingData = new byte[data.Length - parsedLength];
                                            Array.Copy(data, parsedLength, remainingData, 0, remainingData.Length);
                                            Console.WriteLine($"Remaining data: {BitConverter.ToString(remainingData).Replace("-", " ")}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Lỗi khi phân tích theo cấu trúc đăng nhập: {ex.Message}");
                    }
                    
                    // Phân tích theo các kiểu dữ liệu khác nhau
                    Console.WriteLine("\nPhân tích theo các kiểu dữ liệu khác nhau:");
                    try
                    {
                        // Phân tích theo short (2 bytes)
                        Console.WriteLine("\nPhân tích theo short (2 bytes):");
                        for (int i = 0; i < data.Length - 1; i += 2)
                        {
                            short value = BitConverter.ToInt16(data, i);
                            Console.WriteLine($"Offset {i}: {value} (0x{value:X4})");
                        }
                        
                        // Phân tích theo int (4 bytes)
                        Console.WriteLine("\nPhân tích theo int (4 bytes):");
                        for (int i = 0; i < data.Length - 3; i += 4)
                        {
                            int value = BitConverter.ToInt32(data, i);
                            Console.WriteLine($"Offset {i}: {value} (0x{value:X8})");
                        }
                        
                        // Phân tích theo chuỗi ASCII
                        Console.WriteLine("\nPhân tích theo chuỗi ASCII:");
                        string asciiString = Encoding.ASCII.GetString(data);
                        Console.WriteLine($"ASCII: {asciiString}");
                        
                        // Phân tích theo chuỗi Unicode
                        Console.WriteLine("\nPhân tích theo chuỗi Unicode:");
                        string unicodeString = Encoding.Unicode.GetString(data);
                        Console.WriteLine($"Unicode: {unicodeString}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Lỗi khi phân tích theo các kiểu dữ liệu khác nhau: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine("Packet không đủ dài để chứa dữ liệu được khai báo");
                }
            }
            else
            {
                Console.WriteLine("Packet quá ngắn để phân tích");
            }
            
            Console.WriteLine("=== Kết thúc phân tích packet cụ thể ===");
        }
        
        private static byte[] HexStringToByteArray(string hex)
        {
            int length = hex.Length;
            byte[] bytes = new byte[length / 2];
            for (int i = 0; i < length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            return bytes;
        }
    }
}
