﻿using HeroLogin.Core;
using HeroLogin.Services;
using Microsoft.AspNetCore.Server.Kestrel.Core;

namespace HeroLogin
{
    public class Program
    {

        public static async Task Main(string[] args)
        {
            // Check for headless mode
            bool noConsole = Array.Exists(args, arg => arg.Equals("--no-console", StringComparison.OrdinalIgnoreCase));

            // Hide console window if requested
            if (noConsole)
            {
                HideConsoleWindow();
            }

            // Khởi tạo logger
            var logger = Logger.Instance;
            logger.Info($"HeroLogin đang khởi động...");

            try
            {
                // Khởi tạo cấu hình
                var configManager = ConfigManager.Instance;
                logger.Info("Đã tải cấu hình thành công");

                // Khởi động LoginServer
                var loginServer = LoginServer.Instance;
                bool success = await loginServer.StartAsync();

                if (success)
                {
                    logger.Info("LoginServer đã khởi động thành công");

                    // Tự động tìm và kết nối với GameServers
                    await AutoConnectToGameServersAsync();
                        // Khởi động gRPC server
                    await CreateHostBuilder(args).Build().RunAsync();
                }
                else
                {
                    logger.Error("Không thể khởi động LoginServer");
                    return;
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Lỗi khi khởi động ứng dụng: {ex.Message}");
            }
        }

        private static async Task AutoConnectToGameServersAsync()
        {
            try
            {
                var logger = Logger.Instance;
                var configManager = ConfigManager.Instance;

                logger.Info("Đang tự động tìm kiếm GameServers...");

                // Lấy danh sách GameServers từ config
                var clusters = configManager.ServerClusterSettings;

                foreach (var cluster in clusters)
                {
                    foreach (var channel in cluster.Channels)
                    {
                        logger.Info($"Attempting to connect to GameServer: {channel.ServerName} at {channel.ServerIP}:{channel.GameServerPort}");

                        // Tạo một task để thử kết nối (không chờ để không block)
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                // Đợi một chút để GameServer có thể khởi động
                                await Task.Delay(5000);

                                // Thử ping hoặc kết nối đến GameServer
                                // Ở đây chúng ta chỉ log, GameServer sẽ tự kết nối khi sẵn sàng
                                logger.Info($"Waiting for GameServer {channel.ServerName} to connect...");
                            }
                            catch (Exception ex)
                            {
                                logger.Warning($"Could not connect to GameServer {channel.ServerName}: {ex.Message}");
                            }
                        });
                    }
                }

                logger.Info("Auto-connection process initiated for all configured GameServers");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error in auto-connect process: {ex.Message}");
            }
        }

        private static void HideConsoleWindow()
        {
            try
            {
                var handle = GetConsoleWindow();
                ShowWindow(handle, SW_HIDE);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not hide console window: {ex.Message}");
            }
        }

        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        private const int SW_HIDE = 0;

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureKestrel(options =>
                    {
                        var configManager = ConfigManager.Instance;
                        var grpcPort = configManager.LoginServerSettings.LoginServerGrpcPort;
                        var webSocketPort = configManager.LoginServerSettings.WebSocketPort;

                        // Cấu hình Kestrel để lắng nghe kết nối gRPC
                        options.ListenAnyIP(grpcPort, listenOptions =>
                        {
                            listenOptions.Protocols = HttpProtocols.Http2;
                        });

                        // Cấu hình Kestrel để lắng nghe kết nối WebSocket
                        options.ListenAnyIP(webSocketPort, listenOptions =>
                        {
                            listenOptions.Protocols = HttpProtocols.Http1;
                        });
                    });

                    webBuilder.ConfigureServices(services =>
                    {
                        services.AddGrpc();
                    });

                    webBuilder.Configure(app =>
                    {
                        app.UseRouting();

                        // Thêm WebSocket middleware
                        app.UseWebSockets();

                        app.UseEndpoints(endpoints =>
                        {
                            endpoints.MapGrpcService<LoginAuthService>();
                            endpoints.MapGrpcService<WebAdminService>();

                            // Map WebSocket endpoint
                            endpoints.Map("/ws", async context =>
                            {
                                if (context.WebSockets.IsWebSocketRequest)
                                {
                                    var webSocket = await context.WebSockets.AcceptWebSocketAsync();
                                    await WebSocketService.Instance.HandleWebSocketAsync(context, webSocket);
                                }
                                else
                                {
                                    context.Response.StatusCode = 400;
                                }
                            });
                        });
                    });
                });
    }
}
