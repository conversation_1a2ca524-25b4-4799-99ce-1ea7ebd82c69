syntax = "proto3";

option csharp_namespace = "HeroLogin.Protos";

package webadmin;

// WebAdmin Service Definition
service WebAdmin {
  // Authentication
  rpc Login (LoginRequest) returns (LoginResponse);
  rpc RefreshToken (RefreshTokenRequest) returns (RefreshTokenResponse);
  rpc Logout (LogoutRequest) returns (LogoutResponse);

  // Server Management
  rpc GetServerList (GetServerListRequest) returns (GetServerListResponse);
  rpc GetServerStatus (GetServerStatusRequest) returns (GetServerStatusResponse);
  rpc StartGameServer (StartGameServerRequest) returns (StartGameServerResponse);
  rpc StopGameServer (StopGameServerRequest) returns (StopGameServerResponse);
  rpc RestartGameServer (RestartGameServerRequest) returns (RestartGameServerResponse);

  // Configuration Management
  rpc GetServerConfig (GetServerConfigRequest) returns (GetServerConfigResponse);
  rpc UpdateServerConfig (UpdateServerConfigRequest) returns (UpdateServerConfigResponse);
  rpc ReloadServerConfig (ReloadServerConfigRequest) returns (ReloadServerConfigResponse);

  // Player Management
  rpc GetOnlinePlayers (GetOnlinePlayersRequest) returns (GetOnlinePlayersResponse);
  rpc KickPlayer (KickPlayerRequest) returns (KickPlayerResponse);
  rpc BanPlayer (BanPlayerRequest) returns (BanPlayerResponse);
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse);

  // Monitoring & Logs
  rpc GetServerMetrics (GetServerMetricsRequest) returns (GetServerMetricsResponse);
  rpc StreamServerLogs (StreamServerLogsRequest) returns (stream LogEntry);
  rpc StreamServerMetrics (StreamServerMetricsRequest) returns (stream ServerMetrics);

  // User Management (Admin only)
  rpc GetUsers (GetUsersRequest) returns (GetUsersResponse);
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
  rpc UpdateUser (UpdateUserRequest) returns (UpdateUserResponse);
  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse);
}

// Authentication Messages
message LoginRequest {
  string username = 1;
  string password = 2;
  string ip_address = 3;
}

message LoginResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
  int32 expires_in = 5;
  UserInfo user = 6;
}

message RefreshTokenRequest {
  string access_token = 1;
  string refresh_token = 2;
}

message RefreshTokenResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string refresh_token = 4;
  int32 expires_in = 5;
}

message LogoutRequest {
  string access_token = 1;
}

message LogoutResponse {
  bool success = 1;
  string message = 2;
}

// User Information
message UserInfo {
  int32 id = 1;
  string username = 2;
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  repeated string roles = 6;
  repeated string permissions = 7;
  bool is_active = 8;
  string last_login_at = 9;
}

// Server Management Messages
message GetServerListRequest {
  // Empty for now, can add filters later
}

message GetServerListResponse {
  bool success = 1;
  string message = 2;
  repeated ServerInfo servers = 3;
}

message ServerInfo {
  int32 server_id = 1;
  string server_name = 2;
  string server_ip = 3;
  int32 game_server_port = 4;
  int32 grpc_port = 5;
  ServerStatus status = 6;
  int32 online_players = 7;
  int32 max_players = 8;
  string uptime = 9;
  string version = 10;
  int32 cluster_id = 11;
}

enum ServerStatus {
  UNKNOWN = 0;
  OFFLINE = 1;
  STARTING = 2;
  ONLINE = 3;
  STOPPING = 4;
  ERROR = 5;
  MAINTENANCE = 6;
}

message GetServerStatusRequest {
  int32 server_id = 1;
}

message GetServerStatusResponse {
  bool success = 1;
  string message = 2;
  ServerInfo server = 3;
  ServerMetrics metrics = 4;
}

message StartGameServerRequest {
  int32 server_id = 1;
  string config_path = 2;
  bool auto_start = 3;
}

message StartGameServerResponse {
  bool success = 1;
  string message = 2;
  int32 process_id = 3;
}

message StopGameServerRequest {
  int32 server_id = 1;
  bool force = 2;
  string reason = 3;
}

message StopGameServerResponse {
  bool success = 1;
  string message = 2;
}

message RestartGameServerRequest {
  int32 server_id = 1;
  string config_path = 2;
  bool force = 3;
}

message RestartGameServerResponse {
  bool success = 1;
  string message = 2;
  int32 process_id = 3;
}

// Configuration Messages
message GetServerConfigRequest {
  int32 server_id = 1;
  string config_type = 2; // "appsettings", "world_config"
}

message GetServerConfigResponse {
  bool success = 1;
  string message = 2;
  string config_json = 3;
  string config_type = 4;
  string last_modified = 5;
}

message UpdateServerConfigRequest {
  int32 server_id = 1;
  string config_type = 2;
  string config_json = 3;
  bool backup_current = 4;
}

message UpdateServerConfigResponse {
  bool success = 1;
  string message = 2;
  string backup_path = 3;
}

message ReloadServerConfigRequest {
  int32 server_id = 1;
}

message ReloadServerConfigResponse {
  bool success = 1;
  string message = 2;
}

// Player Management Messages
message GetOnlinePlayersRequest {
  int32 server_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string search = 4;
}

message GetOnlinePlayersResponse {
  bool success = 1;
  string message = 2;
  repeated PlayerInfo players = 3;
  int32 total_count = 4;
}

message PlayerInfo {
  int32 player_id = 1;
  string character_name = 2;
  string account_name = 3;
  int32 level = 4;
  string class_name = 5;
  string guild_name = 6;
  string map_name = 7;
  string ip_address = 8;
  string login_time = 9;
  bool is_online = 10;
}

message KickPlayerRequest {
  int32 server_id = 1;
  int32 player_id = 2;
  string reason = 3;
}

message KickPlayerResponse {
  bool success = 1;
  string message = 2;
}

message BanPlayerRequest {
  int32 server_id = 1;
  int32 player_id = 2;
  string reason = 3;
  int32 duration_hours = 4; // 0 = permanent
}

message BanPlayerResponse {
  bool success = 1;
  string message = 2;
}

message SendMessageRequest {
  int32 server_id = 1;
  int32 player_id = 2; // 0 = broadcast to all
  string message = 3;
  MessageType type = 4;
}

enum MessageType {
  SYSTEM = 0;
  ANNOUNCEMENT = 1;
  PRIVATE = 2;
  BROADCAST = 3;
}

message SendMessageResponse {
  bool success = 1;
  string message = 2;
}

// Monitoring Messages
message GetServerMetricsRequest {
  int32 server_id = 1;
}

message GetServerMetricsResponse {
  bool success = 1;
  string message = 2;
  ServerMetrics metrics = 3;
}

message ServerMetrics {
  double cpu_usage = 1;
  double memory_usage = 2;
  int64 memory_total = 3;
  int64 memory_used = 4;
  int32 active_connections = 5;
  int32 total_connections = 6;
  double network_in = 7;
  double network_out = 8;
  string timestamp = 9;
}

message StreamServerLogsRequest {
  int32 server_id = 1;
  string log_level = 2; // "DEBUG", "INFO", "WARN", "ERROR"
  int32 tail_lines = 3; // Number of recent lines to include
}

message LogEntry {
  string timestamp = 1;
  string level = 2;
  string message = 3;
  string source = 4;
  int32 server_id = 5;
}

message StreamServerMetricsRequest {
  int32 server_id = 1;
  int32 interval_seconds = 2; // Update interval
}

// User Management Messages (Admin only)
message GetUsersRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

message GetUsersResponse {
  bool success = 1;
  string message = 2;
  repeated UserInfo users = 3;
  int32 total_count = 4;
}

message CreateUserRequest {
  string username = 1;
  string email = 2;
  string password = 3;
  string first_name = 4;
  string last_name = 5;
  repeated string roles = 6;
}

message CreateUserResponse {
  bool success = 1;
  string message = 2;
  UserInfo user = 3;
}

message UpdateUserRequest {
  int32 user_id = 1;
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  repeated string roles = 5;
  bool is_active = 6;
}

message UpdateUserResponse {
  bool success = 1;
  string message = 2;
  UserInfo user = 3;
}

message DeleteUserRequest {
  int32 user_id = 1;
}

message DeleteUserResponse {
  bool success = 1;
  string message = 2;
}