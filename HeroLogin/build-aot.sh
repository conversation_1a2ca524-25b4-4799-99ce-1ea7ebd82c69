#!/bin/bash

echo "🚀 Building HeroLogin with AOT Native Compilation..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
dotnet clean --configuration Release

# Restore packages
echo "📦 Restoring packages..."
dotnet restore

# Build in Release mode with AOT
echo "🔨 Building with AOT (Release mode)..."
dotnet publish --configuration Release --runtime linux-x64 --self-contained true

if [ $? -eq 0 ]; then
    echo "✅ AOT Build completed successfully!"
    echo "📁 Output location: bin/Release/net9.0/linux-x64/publish/"
    
    # Check if the native executable exists
    if [ -f "bin/Release/net9.0/linux-x64/publish/HeroLogin" ]; then
        echo "🎯 Native executable created: HeroLogin"
        echo "📊 File size:"
        ls -lh bin/Release/net9.0/linux-x64/publish/HeroLogin
        
        echo ""
        echo "🏃 To run the native executable:"
        echo "cd bin/Release/net9.0/linux-x64/publish/"
        echo "./HeroLogin"
    else
        echo "❌ Native executable not found!"
    fi
else
    echo "❌ AOT Build failed!"
    exit 1
fi
